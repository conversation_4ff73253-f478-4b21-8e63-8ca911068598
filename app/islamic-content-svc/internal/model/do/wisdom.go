// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Wisdom is the golang structure of table wisdom for DAO operations like Where/Data.
type Wisdom struct {
	g.Meta        `orm:"table:wisdom, do:true"`
	Id            interface{} //
	IsZh          interface{} // 是否中文，0-否，1-是
	IsEn          interface{} // 是否英文，0-否，1-是
	IsId          interface{} // 是否印尼文，0-否，1-是
	WisdomCateId  interface{} //
	IsPublish     interface{} // 状态 [ 0未发布 1 已发布  2 已下线]
	Sort          interface{} // 排序
	Views         interface{} // 浏览量
	ArticleId     interface{} // 文章id
	CreateAccount interface{} // 创建者
	UpdateAccount interface{} // 更新者
	CreateTime    interface{} //
	UpdateTime    interface{} //
	DeleteTime    interface{} //
}
