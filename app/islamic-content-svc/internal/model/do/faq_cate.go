// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// FaqCate is the golang structure of table faq_cate for DAO operations like Where/Data.
type FaqCate struct {
	g.Meta        `orm:"table:faq_cate, do:true"`
	Id            interface{} //
	IsZh          interface{} // 是否中文，0-否，1-是
	IsEn          interface{} // 是否英文，0-否，1-是
	IsId          interface{} // 是否印尼文，0-否，1-是
	IsPublish     interface{} // 状态 [ 0 待发布 1 已发布  2 已下线]
	Sort          interface{} // 排序
	CateCount     interface{} // 分类下的文章总数
	CreateAccount interface{} // 创建者
	UpdateAccount interface{} // 更新者
	CreateTime    interface{} //
	UpdateTime    interface{} //
	DeleteTime    interface{} //
}
