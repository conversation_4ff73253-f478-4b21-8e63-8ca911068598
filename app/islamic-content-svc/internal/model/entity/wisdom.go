// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Wisdom is the golang structure for table wisdom.
type Wisdom struct {
	Id            uint   `json:"id"            orm:"id"             description:""`                        //
	IsZh          uint   `json:"isZh"          orm:"is_zh"          description:"是否中文，0-否，1-是"`            // 是否中文，0-否，1-是
	IsEn          uint   `json:"isEn"          orm:"is_en"          description:"是否英文，0-否，1-是"`            // 是否英文，0-否，1-是
	IsId          uint   `json:"isId"          orm:"is_id"          description:"是否印尼文，0-否，1-是"`           // 是否印尼文，0-否，1-是
	WisdomCateId  uint   `json:"wisdomCateId"  orm:"wisdom_cate_id" description:""`                        //
	IsPublish     int    `json:"isPublish"     orm:"is_publish"     description:"状态 [ 0未发布 1 已发布  2 已下线]"` // 状态 [ 0未发布 1 已发布  2 已下线]
	Sort          int    `json:"sort"          orm:"sort"           description:"排序"`                      // 排序
	Views         int    `json:"views"         orm:"views"          description:"浏览量"`                     // 浏览量
	ArticleId     uint   `json:"articleId"     orm:"article_id"     description:"文章id"`                    // 文章id
	CreateAccount string `json:"createAccount" orm:"create_account" description:"创建者"`                     // 创建者
	UpdateAccount string `json:"updateAccount" orm:"update_account" description:"更新者"`                     // 更新者
	CreateTime    int64  `json:"createTime"    orm:"create_time"    description:""`                        //
	UpdateTime    int64  `json:"updateTime"    orm:"update_time"    description:""`                        //
	DeleteTime    int64  `json:"deleteTime"    orm:"delete_time"    description:""`                        //
}
