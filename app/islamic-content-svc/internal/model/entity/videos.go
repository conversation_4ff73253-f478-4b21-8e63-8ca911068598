// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Videos is the golang structure for table videos.
type Videos struct {
	Id               uint   `json:"id"               orm:"id"                 description:"主键ID"`                   // 主键ID
	CategoryId       uint   `json:"categoryId"       orm:"category_id"        description:"分类ID"`                   // 分类ID
	VideoUrl         string `json:"videoUrl"         orm:"video_url"          description:"视频文件URL"`                // 视频文件URL
	VideoSize        uint64 `json:"videoSize"        orm:"video_size"         description:"视频文件大小(字节)"`             // 视频文件大小(字节)
	VideoDuration    uint   `json:"videoDuration"    orm:"video_duration"     description:"视频时长(秒)"`                // 视频时长(秒)
	VideoFormat      string `json:"videoFormat"      orm:"video_format"       description:"视频格式：mp4, mov等"`         // 视频格式：mp4, mov等
	VideoCoverUrl    string `json:"videoCoverUrl"    orm:"video_cover_url"    description:"视频封面图片URL"`              // 视频封面图片URL
	ViewCount        uint64 `json:"viewCount"        orm:"view_count"         description:"播放次数"`                   // 播放次数
	ShareCount       uint64 `json:"shareCount"       orm:"share_count"        description:"分享次数"`                   // 分享次数
	CollectCount     uint64 `json:"collectCount"     orm:"collect_count"      description:"收藏次数"`                   // 收藏次数
	CreatorName      string `json:"creatorName"      orm:"creator_name"       description:"创建者姓名"`                  // 创建者姓名
	Author           string `json:"author"           orm:"author"             description:"视频作者"`                   // 视频作者
	AuthorLogo       string `json:"authorLogo"       orm:"author_logo"        description:"作者头像URL"`                // 作者头像URL
	AuthorAuthStatus uint   `json:"authorAuthStatus" orm:"author_auth_status" description:"作者认证状态：0-未认证，1-已认证"`     // 作者认证状态：0-未认证，1-已认证
	PublishState     uint   `json:"publishState"     orm:"publish_state"      description:"发布状态：0-待发布，1-已发布，2-已下线"` // 发布状态：0-待发布，1-已发布，2-已下线
	IsRecommended    uint   `json:"isRecommended"    orm:"is_recommended"     description:"是否推荐，0-否，1-是"`           // 是否推荐，0-否，1-是
	IsDraft          int    `json:"isDraft"          orm:"is_draft"           description:"是否草稿状态，1是，0否"`           // 是否草稿状态，1是，0否
	CreateTime       uint64 `json:"createTime"       orm:"create_time"        description:"创建时间(毫秒时间戳)"`            // 创建时间(毫秒时间戳)
	PublishTime      uint64 `json:"publishTime"      orm:"publish_time"       description:"发布时间(毫秒时间戳)"`            // 发布时间(毫秒时间戳)
	UpdateTime       uint64 `json:"updateTime"       orm:"update_time"        description:"更新时间(毫秒时间戳)"`            // 更新时间(毫秒时间戳)
	DeleteTime       int64  `json:"deleteTime"       orm:"delete_time"        description:"删除时间"`                   // 删除时间
	VideoName        string `json:"videoName"        orm:"video_name"         description:"视频名称"`                   // 视频名称
}
