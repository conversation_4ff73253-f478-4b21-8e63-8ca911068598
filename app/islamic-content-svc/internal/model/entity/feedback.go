// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// Feedback is the golang structure for table feedback.
type Feedback struct {
	Id               int    `json:"id"               orm:"id"                description:""`                    //
	FeedbackType     int    `json:"feedbackType"     orm:"feedback_type"     description:"反馈类型  1 系统故障 2 功能建议"` // 反馈类型  1 系统故障 2 功能建议
	UserId           uint   `json:"userId"           orm:"user_id"           description:"用户id"`                // 用户id
	UserAccount      string `json:"userAccount"      orm:"user_account"      description:"用户账号"`                // 用户账号
	Images           string `json:"images"           orm:"images"            description:"反馈图片 json字符串"`        // 反馈图片 json字符串
	Desc             string `json:"desc"             orm:"desc"              description:"反馈内容"`                // 反馈内容
	FeedbackTime     int64  `json:"feedbackTime"     orm:"feedback_time"     description:"反馈时间ms"`              // 反馈时间ms
	FeedbackStatus   int    `json:"feedbackStatus"   orm:"feedback_status"   description:"反馈状态 1 未处理 2已处理"`     // 反馈状态 1 未处理 2已处理
	FeedbackResult   int    `json:"feedbackResult"   orm:"feedback_result"   description:"反馈结果 1 无效反馈 2有效反馈"`   // 反馈结果 1 无效反馈 2有效反馈
	CompleteTime     int64  `json:"completeTime"     orm:"complete_time"     description:"处理时间ms"`              // 处理时间ms
	CompleteRemark   string `json:"completeRemark"   orm:"complete_remark"   description:"处理备注"`                // 处理备注
	CompleteAccount  string `json:"completeAccount"  orm:"complete_account"  description:"处理人"`                 // 处理人
	CompleteNickName string `json:"completeNickName" orm:"complete_nickName" description:"处理人昵称"`               // 处理人昵称
	CreateTime       int64  `json:"createTime"       orm:"create_time"       description:"创建时间"`                // 创建时间
	UpdateTime       int64  `json:"updateTime"       orm:"update_time"       description:"更新时间"`                // 更新时间
	DeleteTime       int64  `json:"deleteTime"       orm:"delete_time"       description:"删除时间"`                // 删除时间
}
