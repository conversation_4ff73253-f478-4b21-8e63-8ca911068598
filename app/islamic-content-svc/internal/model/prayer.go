package model

// DailyPrayerTimeInput 祷告时间查询输入参数
type DailyPrayerTimeInput struct {
	Year           int     `json:"year" dc:"年份"`
	Month          int     `json:"month" dc:"月份"`
	Day            int     `json:"day" dc:"日期"`
	Latitude       float64 `json:"latitude" dc:"纬度"`
	Longitude      float64 `json:"longitude" dc:"经度"`
	Timezone       string  `json:"timezone" dc:"时区"`
	MethodCode     string  `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int     `json:"date_adjustment" dc:"日期校正"`
}

// MonthlyPrayerTimesInput 祷告时间查询输入参数
type MonthlyPrayerTimesInput struct {
	Year           int     `json:"year" dc:"年份"`
	Month          int     `json:"month" dc:"月份"`
	Latitude       float64 `json:"latitude" dc:"纬度"`
	Longitude      float64 `json:"longitude" dc:"经度"`
	Timezone       string  `json:"timezone" dc:"时区"`
	MethodCode     string  `json:"method_code" dc:"计算方法代码"`
	DateAdjustment int     `json:"date_adjustment" dc:"日期校正"`
}

// PrayerTimeOutput 祷告时间查询输出结果
type PrayerTimeOutput struct {
	Date        string       `json:"date" dc:"日期"`
	PrayerTime  *PrayerTime  `json:"prayer_time" dc:"祷告时间"`
	IslamicDate *IslamicDate `json:"islamic_date" dc:"伊斯兰历日期"`
}

// PrayerTime 祷告时间
type PrayerTime struct {
	Imsak   string `json:"imsak" dc:"伊姆萨克时间（仅斋月期间）"`
	Subuh   string `json:"subuh" dc:"晨祷时间"`
	Terbit  string `json:"terbit" dc:"日出时间"`
	Dhuha   string `json:"dhuha" dc:"上午祷告时间（可选）"`
	Zuhur   string `json:"zuhur" dc:"晌祷时间"`
	Ashar   string `json:"ashar" dc:"晡祷时间"`
	Maghrib string `json:"maghrib" dc:"昏祷时间"`
	Isya    string `json:"isya" dc:"宵祷时间"`
}

// IslamicDate 伊斯兰历日期
type IslamicDate struct {
	Year  int32 `json:"year" dc:"伊斯兰历年"`
	Month int32 `json:"month" dc:"伊斯兰历月"`
	Day   int32 `json:"day" dc:"伊斯兰历日"`
}

// PrayerCalculationConfig 祷告时间计算配置
type PrayerCalculationConfig struct {
	Year      int32   `json:"year" dc:"年份"`
	Month     int32   `json:"month" dc:"月份"`
	Latitude  float64 `json:"latitude" dc:"用户位置纬度"`
	Longitude float64 `json:"longitude" dc:"用户位置经度"`
	Timezone  string  `json:"timezone" dc:"时区"`
}

// HajiJadwalInfo 朝觐日程信息
type HajiJadwalInfo struct {
	Id             uint64 `json:"id" dc:"朝觐日程ID"`
	ItemNo         int    `json:"item_no" dc:"项目编号"`
	TimeInfo       string `json:"time_info" dc:"时间信息"`
	EventSummary   string `json:"event_summary" dc:"事件简述"`
	AdditionalInfo string `json:"additional_info" dc:"附加信息"`
	ArticleText    string `json:"article_text" dc:"文章详情"`
	CreateTime     uint64 `json:"create_time" dc:"创建时间"`
	UpdateTime     uint64 `json:"update_time" dc:"更新时间"`
}

// HajiJadwalListOutput 朝觐日程列表输出
type HajiJadwalListOutput struct {
	List        []*HajiJadwalInfo `json:"list" dc:"朝觐日程列表"`
	Description string            `json:"description" dc:"日程说明文字"`
	Year        string            `json:"year" dc:"朝觐年份"`
}

// HajiJadwalDetailOutput 朝觐日程详情输出
type HajiJadwalDetailOutput struct {
	Jadwal *HajiJadwalInfo `json:"jadwal" dc:"朝觐日程详情"`
}

// HajiUrutanInfo 朝觐仪式顺序信息
type HajiUrutanInfo struct {
	Id            uint64 `json:"id" dc:"朝觐仪式顺序ID"`
	UrutanNo      int    `json:"urutan_no" dc:"仪式顺序编号"`
	UrutanName    string `json:"urutan_name" dc:"仪式名称"`
	UrutanTime    string `json:"urutan_time" dc:"仪式时间信息"`
	IconUrl       string `json:"icon_url" dc:"图标URL"`
	UrutanContent string `json:"urutan_content" dc:"仪式内容描述"`
	CreateTime    uint64 `json:"create_time" dc:"创建时间"`
	UpdateTime    uint64 `json:"update_time" dc:"更新时间"`
}

// HajiUrutanListOutput 朝觐仪式顺序列表输出
type HajiUrutanListOutput struct {
	List []*HajiUrutanInfo `json:"list" dc:"朝觐仪式顺序列表"`
}

// HajiUrutanDetailOutput 朝觐仪式顺序详情输出
type HajiUrutanDetailOutput struct {
	Urutan *HajiUrutanInfo `json:"urutan" dc:"朝觐仪式顺序详情"`
}

// UmrahUrutanInfo 副朝仪式顺序信息
type UmrahUrutanInfo struct {
	Id            uint64 `json:"id" dc:"副朝仪式顺序ID"`
	UrutanNo      int    `json:"urutan_no" dc:"仪式顺序编号"`
	UrutanName    string `json:"urutan_name" dc:"仪式名称"`
	UrutanTime    string `json:"urutan_time" dc:"仪式时间信息"`
	IconUrl       string `json:"icon_url" dc:"图标URL"`
	UrutanContent string `json:"urutan_content" dc:"仪式内容描述"`
	CreateTime    uint64 `json:"create_time" dc:"创建时间"`
	UpdateTime    uint64 `json:"update_time" dc:"更新时间"`
}

// UmrahUrutanListOutput 副朝仪式顺序列表输出
type UmrahUrutanListOutput struct {
	List []*UmrahUrutanInfo `json:"list" dc:"副朝仪式顺序列表"`
}

// UmrahUrutanDetailOutput 副朝仪式顺序详情输出
type UmrahUrutanDetailOutput struct {
	Urutan *UmrahUrutanInfo `json:"urutan" dc:"副朝仪式顺序详情"`
}

// HajiDoaRingkasContentInfo 朝觐祈祷文内容信息
type HajiDoaRingkasContentInfo struct {
	Id             uint64 `json:"id" dc:"内容ID"`
	ContentOrder   int    `json:"content_order" dc:"内容排序"`
	Title          string `json:"title" dc:"内容标题"`
	MuqattaAt      string `json:"muqatta_at" dc:"Muqattaʿāt断章字母"`
	ArabicText     string `json:"arabic_text" dc:"阿拉伯文原文"`
	IndonesianText string `json:"indonesian_text" dc:"印尼语翻译"`
	LatinText      string `json:"latin_text" dc:"拉丁音译文本"`
	ContentType    int    `json:"content_type" dc:"内容类型：1/2/3，分别对应3种类型的卡片"`
	BacaanId       uint64 `json:"bacaan_id" dc:"诵读ID (收藏接口使用这个bacaan_id，其实跟id是同一个)"`
	IsCollected    bool   `json:"is_collected" dc:"是否已收藏"`
}

// HajiDoaRingkasInfo 朝觐祈祷文简要信息
type HajiDoaRingkasInfo struct {
	Id       uint64                       `json:"id" dc:"祈祷文ID"`
	DoaNo    int                          `json:"doa_no" dc:"祈祷文序号"`
	DoaName  string                       `json:"doa_name" dc:"祈祷文名称"`
	Contents []*HajiDoaRingkasContentInfo `json:"contents" dc:"祈祷文内容列表"`
}

// HajiDoaPanjangInfo 朝觐祈祷文详细信息
type HajiDoaPanjangInfo struct {
	Id          uint64 `json:"id" dc:"祈祷文ID"`
	DoaNo       int    `json:"doa_no" dc:"祈祷文序号"`
	DoaName     string `json:"doa_name" dc:"祈祷文名称"`
	BacaanCount int    `json:"bacaan_count" dc:"诵读数"`
}

// HajiDoaPanjangBacaanContentInfo 朝觐祈祷文诵读内容信息
type HajiDoaPanjangBacaanContentInfo struct {
	Id             uint64 `json:"id" dc:"内容ID"`
	ContentOrder   int    `json:"content_order" dc:"内容排序"`
	Title          string `json:"title" dc:"内容标题"`
	MuqattaAt      string `json:"muqatta_at" dc:"Muqattaʿāt断章字母"`
	ArabicText     string `json:"arabic_text" dc:"阿拉伯文原文"`
	IndonesianText string `json:"indonesian_text" dc:"印尼语翻译"`
	LatinText      string `json:"latin_text" dc:"拉丁音译文本"`
}

// HajiDoaPanjangBacaanInfo 朝觐祈祷文诵读信息
type HajiDoaPanjangBacaanInfo struct {
	Id         uint64                             `json:"id" dc:"诵读ID"`
	DoaId      uint64                             `json:"doa_id" dc:"祈祷文ID"`
	BacaanNo   int                                `json:"bacaan_no" dc:"诵读序号"`
	BacaanName string                             `json:"bacaan_name" dc:"诵读名称"`
	Contents   []*HajiDoaPanjangBacaanContentInfo `json:"contents" dc:"诵读内容列表"`
}

// HajiDoaPanjangBacaanListOutput 朝觐祈祷文诵读内容列表输出
type HajiDoaPanjangBacaanListOutput struct {
	List    []*HajiDoaPanjangBacaanInfo `json:"list" dc:"诵读内容列表"`
	DoaNo   int                         `json:"doa_no" dc:"所属祈祷文序号"`
	DoaName string                      `json:"doa_name" dc:"所属祈祷文名称"`
}

// HajiHikmahInfo 朝觐智慧信息
type HajiHikmahInfo struct {
	Id        uint64 `json:"id" dc:"朝觐智慧ID"`
	ArticleId uint64 `json:"article_id" dc:"文章ID"`
	Title     string `json:"title" dc:"标题"`
}

// HajiHikmahListOutput 朝觐智慧列表输出
type HajiHikmahListOutput struct {
	List []*HajiHikmahInfo `json:"list" dc:"朝觐智慧列表"`
}

// UmrahHikmahInfo 副朝智慧信息
type UmrahHikmahInfo struct {
	Id        uint64 `json:"id" dc:"副朝智慧ID"`
	ArticleId uint64 `json:"article_id" dc:"文章ID"`
	Title     string `json:"title" dc:"标题"`
}

// UmrahHikmahListOutput 副朝智慧列表输出
type UmrahHikmahListOutput struct {
	List []*UmrahHikmahInfo `json:"list" dc:"副朝智慧列表"`
}

// HajiLandmarkItem 朝觐地标信息（列表时返回）
type HajiLandmarkItem struct {
	LandmarkId   uint64  `json:"landmark_id" dc:"地标ID"`
	TypeIconUrl  string  `json:"type_icon_url" dc:"类型图标URL"`
	TypeName     string  `json:"type_name" dc:"类型名称"`
	Latitude     float64 `json:"latitude" dc:"纬度"`
	Longitude    float64 `json:"longitude" dc:"经度"`
	ImageUrl     string  `json:"image_url" dc:"图片URL"`
	LandmarkName string  `json:"landmark_name" dc:"地标名称"`
}

// HajiLandmarkListOutput 朝觐地标列表输出（支持分页）
type HajiLandmarkListOutput struct {
	List  []*HajiLandmarkItem `json:"list" dc:"朝觐地标列表"`
	Total int                 `json:"total" dc:"总数"`
}

// HajiLandmarkInfo 朝觐地标信息（详情时返回）
type HajiLandmarkInfo struct {
	LandmarkId       uint64  `json:"landmark_id" dc:"地标ID"`
	TypeIconUrl      string  `json:"type_icon_url" dc:"类型图标URL"`
	TypeName         string  `json:"type_name" dc:"类型名称"`
	Latitude         float64 `json:"latitude" dc:"纬度"`
	Longitude        float64 `json:"longitude" dc:"经度"`
	ImageUrl         string  `json:"image_url" dc:"图片URL"`
	LandmarkName     string  `json:"landmark_name" dc:"地标名称"`
	Country          string  `json:"country" dc:"国家/地区"`
	Address          string  `json:"address" dc:"详细地址"`
	ShortDescription string  `json:"short_description" dc:"简介"`
	InformationText  string  `json:"information_text" dc:"详细介绍"`
}

// LandmarkTypeInfo 地标类型信息
type LandmarkTypeInfo struct {
	Id       uint64 `json:"id" dc:"类型ID"`
	IconUrl  string `json:"icon_url" dc:"图标URL"`
	TypeName string `json:"type_name" dc:"类型名称"`
}

// ==================== 副朝地标相关结构体 ====================

// UmrahLandmarkItem 副朝地标信息（列表时返回）
type UmrahLandmarkItem struct {
	LandmarkId   uint64  `json:"landmark_id" dc:"地标ID"`
	TypeIconUrl  string  `json:"type_icon_url" dc:"类型图标URL"`
	TypeName     string  `json:"type_name" dc:"类型名称"`
	Latitude     float64 `json:"latitude" dc:"纬度"`
	Longitude    float64 `json:"longitude" dc:"经度"`
	ImageUrl     string  `json:"image_url" dc:"图片URL"`
	LandmarkName string  `json:"landmark_name" dc:"地标名称"`
}

// UmrahLandmarkListOutput 副朝地标列表输出（支持分页）
type UmrahLandmarkListOutput struct {
	List  []*UmrahLandmarkItem `json:"list" dc:"副朝地标列表"`
	Total int                  `json:"total" dc:"总数"`
}

// UmrahLandmarkInfo 副朝地标信息（详情时返回）
type UmrahLandmarkInfo struct {
	LandmarkId       uint64  `json:"landmark_id" dc:"地标ID"`
	TypeIconUrl      string  `json:"type_icon_url" dc:"类型图标URL"`
	TypeName         string  `json:"type_name" dc:"类型名称"`
	Latitude         float64 `json:"latitude" dc:"纬度"`
	Longitude        float64 `json:"longitude" dc:"经度"`
	ImageUrl         string  `json:"image_url" dc:"图片URL"`
	LandmarkName     string  `json:"landmark_name" dc:"地标名称"`
	Country          string  `json:"country" dc:"国家/地区"`
	Address          string  `json:"address" dc:"详细地址"`
	ShortDescription string  `json:"short_description" dc:"简介"`
	InformationText  string  `json:"information_text" dc:"详细介绍"`
}

// ==================== 副朝祈祷文相关结构体 ====================

// UmrahDoaRingkasContentInfo 副朝祈祷文内容信息
type UmrahDoaRingkasContentInfo struct {
	Id             uint64 `json:"id" dc:"内容ID"`
	ContentOrder   int    `json:"content_order" dc:"内容排序"`
	Title          string `json:"title" dc:"内容标题"`
	MuqattaAt      string `json:"muqatta_at" dc:"Muqattaʿāt断章字母"`
	ArabicText     string `json:"arabic_text" dc:"阿拉伯文原文"`
	IndonesianText string `json:"indonesian_text" dc:"印尼语翻译"`
	LatinText      string `json:"latin_text" dc:"拉丁音译文本"`
}

// UmrahDoaRingkasInfo 副朝祈祷文简要信息
type UmrahDoaRingkasInfo struct {
	Id       uint64                        `json:"id" dc:"祈祷文ID"`
	DoaNo    int                           `json:"doa_no" dc:"祈祷文序号"`
	DoaName  string                        `json:"doa_name" dc:"祈祷文名称"`
	Contents []*UmrahDoaRingkasContentInfo `json:"contents" dc:"祈祷文内容列表"`
}

// UmrahDoaPanjangInfo 副朝祈祷文详细信息
type UmrahDoaPanjangInfo struct {
	Id          uint64 `json:"id" dc:"祈祷文ID"`
	DoaNo       int    `json:"doa_no" dc:"祈祷文序号"`
	DoaName     string `json:"doa_name" dc:"祈祷文名称"`
	BacaanCount int    `json:"bacaan_count" dc:"诵读数"`
}

// UmrahDoaPanjangBacaanContentInfo 副朝祈祷文诵读内容信息
type UmrahDoaPanjangBacaanContentInfo struct {
	Id             uint64 `json:"id" dc:"内容ID"`
	ContentOrder   int    `json:"content_order" dc:"内容排序"`
	Title          string `json:"title" dc:"内容标题"`
	MuqattaAt      string `json:"muqatta_at" dc:"Muqattaʿāt断章字母"`
	ArabicText     string `json:"arabic_text" dc:"阿拉伯文原文"`
	IndonesianText string `json:"indonesian_text" dc:"印尼语翻译"`
	LatinText      string `json:"latin_text" dc:"拉丁音译文本"`
}

// UmrahDoaPanjangBacaanInfo 副朝祈祷文诵读信息
type UmrahDoaPanjangBacaanInfo struct {
	Id         uint64                              `json:"id" dc:"诵读ID"`
	DoaId      uint64                              `json:"doa_id" dc:"祈祷文ID"`
	BacaanNo   int                                 `json:"bacaan_no" dc:"诵读序号"`
	BacaanName string                              `json:"bacaan_name" dc:"诵读名称"`
	Contents   []*UmrahDoaPanjangBacaanContentInfo `json:"contents" dc:"诵读内容列表"`
}

// UmrahDoaPanjangBacaanListOutput 副朝祈祷文诵读内容列表输出
type UmrahDoaPanjangBacaanListOutput struct {
	List    []*UmrahDoaPanjangBacaanInfo `json:"list" dc:"诵读内容列表"`
	DoaNo   int                          `json:"doa_no" dc:"所属祈祷文序号"`
	DoaName string                       `json:"doa_name" dc:"所属祈祷文名称"`
}

// ==================== 朝觐新闻相关结构体 ====================

// HajiNewsItem 朝觐新闻列表项
type HajiNewsItem struct {
	ArticleId    uint64 `json:"article_id" dc:"文章ID"`
	CategoryName string `json:"category_name" dc:"资讯分类名称"`
	Title        string `json:"title" dc:"标题"`
	PublishTime  int64  `json:"publish_time" dc:"发布时间（毫秒时间戳）"`
	CoverImage   string `json:"cover_image" dc:"封面图片URL"`
}

// HajiNewsListOutput 朝觐新闻列表输出
type HajiNewsListOutput struct {
	List      []*HajiNewsItem `json:"list" dc:"新闻列表"`
	Total     int             `json:"total" dc:"总数"`
	HeaderTag string          `json:"header_tag" dc:"sort_order 最靠前的标签名"`
}

// ==================== 斋月祈祷文相关结构体 ====================

// RamadhanDoaContentInfo 斋月祈祷文内容信息
type RamadhanDoaContentInfo struct {
	Id             uint64 `json:"id" dc:"内容ID"`
	ContentOrder   int    `json:"content_order" dc:"内容排序"`
	Title          string `json:"title" dc:"内容标题"`
	MuqattaAt      string `json:"muqatta_at" dc:"Muqattaʿāt断章字母"`
	ArabicText     string `json:"arabic_text" dc:"阿拉伯文原文"`
	IndonesianText string `json:"indonesian_text" dc:"印尼语翻译"`
	LatinText      string `json:"latin_text" dc:"拉丁音译文本"`
}

// RamadhanDoaInfo 斋月祈祷文简要信息
type RamadhanDoaInfo struct {
	Id       uint64                    `json:"id" dc:"祈祷文ID"`
	DoaNo    int                       `json:"doa_no" dc:"祈祷文序号"`
	DoaName  string                    `json:"doa_name" dc:"祈祷文名称"`
	Contents []*RamadhanDoaContentInfo `json:"contents" dc:"祈祷文内容列表"`
}
