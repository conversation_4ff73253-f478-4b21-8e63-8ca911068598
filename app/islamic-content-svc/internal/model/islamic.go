package model

//type SurahParamInput struct {
//	Id        uint   `json:"id"                    dc:"table id"`
//	Name      string `json:"name"                  dc:"name"`
//	IsPopular uint   `json:"is_popular"                  dc:"是否热门"`
//	Page      int    `json:"page"                  dc:"page 页数量"`
//	Size      int    `json:"size"                  dc:"size 页大小"`
//}

type PageResponse struct {
	Page  int `json:"page"                    dc:"当前页"`
	Size  int `json:"size"                  dc:"每页数量"`
	Total int `json:"total"                  dc:""`
}

type SurahParamOutput struct {
	Id          uint   `json:"id"                    dc:"table id"`
	Name        string `json:"name"                  dc:"name"`
	NameLatin   string `json:"name_latin"                  dc:"name_latin"`
	JumlahAyat  int    `json:"jumlah_ayat"                  dc:"章节编号 (1-114)"`
	Nomor       int    `json:"nomor"                  dc:"经文数量"`
	Arti        string `json:"arti"                  dc:"章节描述"`
	TempatTurun string `json:"tempat_turun"                  dc:"降示地点"`
}

//type SurahParamOutputPage struct {
//	SurahParamOutput []*SurahParamOutput `json:"surah_param_output"                    dc:"章节信息"`
//	PageResponse     PageResponse        `json:"page_response"                  dc:"降示地点"`
//}

type JuzParamInput struct {
	Name string `json:"name"                  dc:"name"`
}

type JuzParamOutput struct {
	StartSurahId   uint   `json:"start_surah_id"                    dc:"开始章id"`
	StartSurahName string `json:"start_surah_name"                  dc:"开始章name"`
	EndSurahId     uint   `json:"end_surah_name"                  dc:"结束章name"`
	EndSurahName   string `json:"end_surah_id"                  dc:"结束章id"`
	StartAyahId    uint   `json:"start_ayah_id"                  dc:"节id"`
	EndAyahId      uint   `json:"end_ayah_id"                  dc:"节id"`
	Name           string `json:"name"                  dc:"juz名称"`
	FirstWord      string `json:"first_word"                  dc:"page 对应经文的第一个单词"`
	Sort           uint   `json:"sort"                  dc:"排序权重"`
	TempatTurun    string `json:"tempat_turun"                  dc:"朝拜方向"`
}

type AyahReadRecordInput struct {
	Uid      uint32 `json:"uid"                  dc:"用户id"`
	AyahId   uint32 `json:"ayah_id"                  dc:"节id"`
	IsUserOp uint32 `json:"is_user_op"                  dc:"是否用户操作 1:是 0:否"`
}

type AyahReadRecordOutput struct {
}

//type AyahReadCollectListInput struct {
//	Uid uint32 `json:"uid"                  dc:"用户id"`
//}

//type AyahReadCollectListOutput struct {
//	SurahId   uint   `json:"surah_id"                  dc:"所属章节ID"`
//	SurahName string `json:"surah_name"                  dc:"章节名称"`
//	AyahId    uint   `json:"ayah_id"                  dc:"节id"`
//	AyahName  uint   `json:"ayah_name"                  dc:"节名称"`
//	JuzId     uint   `json:"juz_id"                  dc:"Juz"`
//}

type AyahReadCollectInput struct {
	Uid    uint32 `json:"uid"                  dc:"用户id"`
	AyahId uint32 `json:"ayah_id"                  dc:"节id"`
}

type DoaReadCollectInput struct {
	BaccanId uint32 `json:"baccan_id"                  dc:"子id"`
	Types    uint32 `json:"types"                  dc:"类型，1-doa，2-wirid"`
}
type NewsCollectOpInput struct {
	Uid       uint32 `json:"uid"                  dc:"用户id"`
	ArticleId uint32 `json:"article_id"                  dc:"文章ID"`
}

type NewsCollectOpOutput struct {
}

type AyahReadCollectOutput struct {
}

type CheckAyahReadCollectStatusInput struct {
	Uid    uint32 `json:"uid"                  dc:"用户id"`
	AyahId uint32 `json:"ayah_id"                  dc:"节id"`
}

type CheckDoaReadCollectStatusInput struct {
	BaccanId uint32 `json:"baccan_id"                  dc:"子id"`
	Types    uint32 `json:"types"                  dc:"类型，1-doa，2-wirid"`
}
type NewsCollectStatusCheckInput struct {
	Uid       uint32 `json:"uid"                  dc:"用户id"`
	ArticleId uint32 `json:"article_id"                  dc:"文章ID"`
}

type CheckAyahReadCollectStatusOutput struct {
	IsCollect uint32 `json:"is_collect"                  dc:"是否收藏 0:否 1:是"`
}

type NewsCollectStatusCheckOutput struct {
	IsCollect uint32 `json:"is_collect"                  dc:"是否收藏 0:否 1:是"`
}

//type AyahReadRecordListInput struct {
//	Uid    uint32 `json:"uid"                  dc:"用户id"`
//	AyahId uint32 `json:"ayah_id"                  dc:"节id"`
//}

//type AyahReadRecordListOutput struct {
//	SurahId   uint   `json:"surah_id"                  dc:"所属章节ID"`
//	SurahName string `json:"surah_name"                  dc:"章节名称"`
//	AyahId    uint   `json:"ayah_id"                  dc:"节id"`
//	JuzId     uint   `json:"juz_id"                  dc:"Juz"`
//}

//type AyahParamInput struct {
//	Id      uint32 `json:"id"                  dc:"id"`
//	SurahId uint32 `json:"surah_id"                  dc:"章节id"`
//	JuzId   uint32 `json:"juz_id"                  dc:"juz_id"`
//	Page    uint32 `json:"page"                  dc:"page 页数量"`
//}
//
//type AyahParamOutPut struct {
//	Id      uint   `json:"id"                    dc:"table id"`
//	SurahId uint   `json:"surah_id"                  dc:"所属章节ID"`
//	Nomor   uint   `json:"nomor"                  dc:"经文在章节中的编号"`
//	Ar      string `json:"arti"                  dc:"阿拉伯语经文"`
//	Tr      string `json:"tr"                  dc:"音译文本"`
//	Idn     string `json:"idn"                  dc:"印尼语翻译"`
//	Juz     uint   `json:"Juz"                  dc:"Juz"`
//	Page    uint   `json:"page"                  dc:"page 页数量"`
//}

type NewsCategoryListInput struct {
	Pid uint32 `json:"pid"                  dc:"父级ID"`
}

type NewsCategoryListOutput struct {
	Id         uint32 `json:"id"                    dc:"table id"`
	ParentId   uint32 `json:"parent_id"                  dc:"父级ID"`
	LanguageId uint32 `json:"language_id"                  dc:"语言ID"`
	Name       string `json:"name"                  dc:"分类名称"`
	CoverImgs  string `json:"cover_imgs"                  dc:"封面图片"`
}

type NewsTopicListInput struct {
}

type NewsTopicListOutput struct {
	Id         uint32 `json:"id"                    dc:"table id"`
	LanguageId uint32 `json:"language_id"                  dc:"语言ID"`
	Name       string `json:"name"                  dc:"分类名称"`
	ShortName  string `json:"short_name"                  dc:"分类名称"`
	TopicImgs  string `json:"topic_imgs"                  dc:"专题图片"`
}

//type NewsListByCateIdInput struct {
//	LanguageId  uint32 `json:"language_id"                  dc:"语言ID"`
//	IsRecommend uint32 `json:"is_recommend"                  dc:"是否推荐"`
//	CateId      uint32 `json:"cate_id"                  dc:"分类ID"`
//}
//type NewsHotListInput struct {
//	LanguageId uint32 `json:"language_id"                  dc:"语言ID"`
//	Ishot      uint32 `json:"is_hot"                  dc:"是否热门3条数据"`
//}
//type NewsCollectListInput struct {
//	LanguageId uint32 `json:"language_id"                  dc:"语言ID"`
//}
//type NewsListByCateIdOutput struct {
//	ArticleId        uint32 `json:"article_id"   dc:"文章ID"`
//	LanguageId       uint32 `json:"language_id"  dc:"语言ID"`
//	Name             string `json:"name"         dc:"名称"`
//	Content          string `json:"content"      dc:"正文"`
//	CategoryId       uint32 `json:"category_id"  dc:"分类ID"`
//	CategoryName     string `json:"category_name"   dc:"分类名称"`
//	CoverImgs        string `json:"cover_imgs"   dc:"专题图片"`
//	Author           string `json:"author"       dc:"创建人"`
//	AuthorLogo       string `json:"author_logo"       dc:"创建人头像"`
//	AuthorAuthStatus uint32 `json:"author_auth_status"       dc:"创建人认证0-未认证 1-已认证"`
//	PublishTime      int64  `json:"publish_time" dc:"发布时间"`
//}

//type NewsListByTopicIdInput struct {
//	LanguageId uint32 `json:"language_id"                  dc:"语言ID"`
//	TopicId    uint32 `json:"topic_id"                  dc:"话题ID"`
//}

type NewsListByTopicIdOutput struct {
	ArticleId        uint32 `json:"article_id"   dc:"文章ID"`
	LanguageId       uint32 `json:"language_id"  dc:"语言ID"`
	Name             string `json:"name"         dc:"名称"`
	Content          string `json:"content"      dc:"正文"`
	CategoryId       uint32 `json:"category_id"  dc:"分类ID"`
	CategoryName     string `json:"category_name"   dc:"分类名称"`
	CoverImgs        string `json:"cover_imgs"   dc:"专题图片"`
	Author           string `json:"author"       dc:"创建人"`
	AuthorLogo       string `json:"author_logo"       dc:"创建人头像"`
	AuthorAuthStatus uint32 `json:"author_auth_status"       dc:"创建人认证0-未认证 1-已认证"`
	PublishTime      int64  `json:"publish_time" dc:"发布时间"`

	TopicName string `json:"topic_name"       dc:"话题名称"`
}

type NewsInfoInput struct {
	ArticleId uint32 `json:"article_id"                  dc:"文章ID"`
}

type NewsInfoOutput struct {
	ArticleId        uint32 `json:"article_id"   dc:"文章ID"`
	LanguageId       uint32 `json:"language_id"  dc:"语言ID"`
	Name             string `json:"name"         dc:"名称"`
	Content          string `json:"content"      dc:"正文"`
	CategoryId       uint32 `json:"category_id"  dc:"分类ID"`
	CategoryName     string `json:"category_name"   dc:"分类名称"`
	CoverImgs        string `json:"cover_imgs"   dc:"专题图片"`
	Author           string `json:"author"       dc:"创建人"`
	AuthorLogo       string `json:"author_logo"       dc:"创建人头像"`
	AuthorAuthStatus uint32 `json:"author_auth_status"       dc:"创建人认证0-未认证 1-已认证"`
	PublishTime      int64  `json:"publish_time" dc:"发布时间"`
}

// Banner相关的内部模型结构

// BannerInfo Banner信息输出结构
type BannerInfo struct {
	Id          uint32 `json:"id" dc:"Banner ID"`
	LanguageId  uint32 `json:"language_id" dc:"语言ID"`
	Title       string `json:"title" dc:"广告标题"`
	Description string `json:"description" dc:"广告描述"`
	ImageUrl    string `json:"image_url" dc:"广告图片URL"`
	LinkUrl     string `json:"link_url" dc:"跳转链接URL"`
	SortOrder   uint32 `json:"sort_order" dc:"排序权重"`
}

// BannerClickInput Banner点击统计输入参数
type BannerClickInput struct {
	BannerId uint32 `json:"banner_id" dc:"Banner ID"`
	DeviceId string `json:"device_id" dc:"设备唯一标识"`
}
