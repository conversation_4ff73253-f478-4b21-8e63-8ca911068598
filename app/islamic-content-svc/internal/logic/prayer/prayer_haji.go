package prayer

import (
	"context"
	"fmt"
	"halalplus/app/islamic-content-svc/internal/dao"
	"halalplus/app/islamic-content-svc/internal/model"
	"halalplus/app/islamic-content-svc/internal/model/entity"
	"halalplus/utility/token"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
)

// GetHajiJadwalList 获取朝觐日程列表
func (s *sPrayer) GetHajiJadwalList(ctx context.Context, languageId uint) (*model.HajiJadwalListOutput, error) {
	var jadwalList []*model.HajiJadwalInfo
	err := dao.HajiJadwal.Ctx(ctx).As("hj").
		Fields("hj.id, hj.item_no, hjc.time_info, hjc.event_summary, '' as additional_info, '' as article_text").
		LeftJoin(dao.HajiJadwalContent.Table()+" hjc", "hjc.jadwal_id = hj.id").
		Where("hjc.language_id", languageId).
		Order("hj.item_no ASC").
		Scan(&jadwalList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐日程列表失败:", err)
		return nil, err
	}

	// 查询年份描述信息
	// 按照当前的伊斯兰历来查吧
	var descResult struct {
		Description string `json:"description"`
		Year        int    `json:"year"`
	}

	err = dao.HajiJadwalDescription.Ctx(ctx).
		Fields("description, year").
		Where(dao.HajiJadwalDescription.Columns().LanguageId, languageId).
		OrderDesc(dao.HajiJadwalDescription.Columns().Year).
		Limit(1).
		Scan(&descResult)

	var description string
	var year string
	if err != nil {
		g.Log().Warning(ctx, "查询朝觐日程描述失败:", err)
		// 不影响主要功能，使用默认值
		description = ""
		year = "2025"
	} else {
		description = descResult.Description
		year = fmt.Sprintf("%d", descResult.Year)
	}

	return &model.HajiJadwalListOutput{
		List:        jadwalList,
		Description: description,
		Year:        year,
	}, nil
}

// GetHajiJadwalDetail 获取朝觐日程详情
func (s *sPrayer) GetHajiJadwalDetail(ctx context.Context, jadwalId uint64, languageId uint) (*model.HajiJadwalDetailOutput, error) {
	// 查询朝觐日程详情，联合查询多语言内容
	var jadwal model.HajiJadwalInfo
	err := dao.HajiJadwal.Ctx(ctx).As("hj").
		Fields("hj.id, hj.item_no, hjc.time_info, hjc.event_summary, hjc.additional_info, hjc.article_text").
		LeftJoin(dao.HajiJadwalContent.Table()+" hjc", "hjc.jadwal_id = hj.id").
		Where("hj.id", jadwalId).
		Where("hjc.language_id", languageId).
		Scan(&jadwal)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐日程详情失败:", err)
		return nil, err
	}

	if jadwal.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.HajiJadwalDetailOutput{
		Jadwal: &jadwal,
	}, nil
}

// GetHajiUrutanList 获取朝觐仪式顺序列表
func (s *sPrayer) GetHajiUrutanList(ctx context.Context, languageId uint) (*model.HajiUrutanListOutput, error) {
	var urutanList []*model.HajiUrutanInfo
	err := dao.HajiUrutan.Ctx(ctx).As("hu").
		Fields("hu.id, hu.urutan_no, huc.urutan_name, huc.urutan_time, hu.icon_url, '' as urutan_content").
		LeftJoin(dao.HajiUrutanContent.Table()+" huc", "huc.urutan_id = hu.id").
		Where("huc.language_id", languageId).
		Order("hu.urutan_no ASC").
		Scan(&urutanList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐仪式顺序列表失败:", err)
		return nil, err
	}

	return &model.HajiUrutanListOutput{
		List: urutanList,
	}, nil
}

// GetHajiUrutanDetail 获取朝觐仪式顺序详情
func (s *sPrayer) GetHajiUrutanDetail(ctx context.Context, urutanId uint64, languageId uint) (*model.HajiUrutanDetailOutput, error) {
	var urutan model.HajiUrutanInfo
	err := dao.HajiUrutan.Ctx(ctx).As("hu").
		Fields("hu.id, hu.urutan_no, huc.urutan_name, huc.urutan_time, hu.icon_url, huc.urutan_content").
		LeftJoin(dao.HajiUrutanContent.Table()+" huc", "huc.urutan_id = hu.id").
		Where("hu.id", urutanId).
		Where("huc.language_id", languageId).
		Scan(&urutan)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐仪式顺序详情失败:", err)
		return nil, err
	}

	if urutan.Id == 0 {
		return nil, gerror.New("not found")
	}

	return &model.HajiUrutanDetailOutput{
		Urutan: &urutan,
	}, nil
}

// DoaQueryResult 祈祷文查询结果
type DoaQueryResult struct {
	Category      *entity.DoaCategories
	SubCategories []*entity.DoaSubCategories
	DoaDataList   []*entity.DoaData
}

// queryDoaData 查询祈祷文数据的公共方法
func (s *sPrayer) queryDoaData(ctx context.Context, groupName, slug string, parentId *uint64) (*DoaQueryResult, error) {
	var category *entity.DoaCategories
	var err error

	if parentId != nil {
		// 直接通过ID查询分类
		err = dao.DoaCategories.Ctx(ctx).
			Fields("id, name, order_number").
			Where("id", *parentId).
			Scan(&category)
	} else {
		// 通过group_name和slug查询分类
		err = dao.DoaCategories.Ctx(ctx).
			Where("group_name", groupName).
			Where("slug", slug).
			Scan(&category)
	}

	if err != nil {
		return nil, err
	}
	if category == nil {
		return &DoaQueryResult{}, nil
	}

	// 查询子分类列表
	var subCategories []*entity.DoaSubCategories
	err = dao.DoaSubCategories.Ctx(ctx).
		Fields("id, name, order_number").
		Where("parent_id", category.Id).
		Order("order_number ASC").
		Scan(&subCategories)
	if err != nil {
		return nil, err
	}

	if len(subCategories) == 0 {
		return &DoaQueryResult{
			Category:      category,
			SubCategories: subCategories,
			DoaDataList:   []*entity.DoaData{},
		}, nil
	}

	// 批量查询祈祷文数据
	subCategoryIds := make([]int, len(subCategories))
	for i, subCat := range subCategories {
		subCategoryIds[i] = subCat.Id
	}

	var doaDataList []*entity.DoaData
	err = dao.DoaData.Ctx(ctx).
		Fields("id, category_id, arabic, translate, transliteration, order_number").
		WhereIn("category_id", subCategoryIds).
		Order("category_id ASC, order_number ASC").
		Scan(&doaDataList)
	if err != nil {
		return nil, err
	}

	return &DoaQueryResult{
		Category:      category,
		SubCategories: subCategories,
		DoaDataList:   doaDataList,
	}, nil
}

// buildDoaContent 构建祈祷文内容的公共方法
func buildDoaContent(doaData *entity.DoaData) (title, arabicText, indonesianText, latinText string) {
	if doaData.Translate == "" {
		// 如果translate为空，title就是arabic
		return doaData.Arabic, "", "", ""
	} else {
		// 否则按照字段映射
		return "", doaData.Arabic, doaData.Translate, doaData.Transliteration
	}
}

// batchCheckDoaCollectStatus 批量查询祈祷文收藏状态的公共方法
func (s *sPrayer) batchCheckDoaCollectStatus(ctx context.Context, bacaanIds []uint64) (map[uint64]bool, error) {
	if len(bacaanIds) == 0 {
		return make(map[uint64]bool), nil
	}

	uid, err := token.GetUserIdByToken(ctx)
	if err != nil {
		// 如果获取用户ID失败，返回所有都未收藏
		result := make(map[uint64]bool)
		for _, id := range bacaanIds {
			result[id] = false
		}
		return result, nil
	}

	// 查询用户已收藏的bacaan_id列表
	var collectedRecords []entity.DoaReadCollect
	err = dao.DoaReadCollect.Ctx(ctx).
		Fields(dao.DoaReadCollect.Columns().BaccanId).
		Where(dao.DoaReadCollect.Columns().UserId, uid).
		WhereIn(dao.DoaReadCollect.Columns().BaccanId, bacaanIds).
		Scan(&collectedRecords)
	if err != nil {
		g.Log().Error(ctx, "批量查询祈祷文收藏状态失败:", err)
		// 出错时返回所有都未收藏
		result := make(map[uint64]bool)
		for _, id := range bacaanIds {
			result[id] = false
		}
		return result, nil
	}

	// 构建结果map
	result := make(map[uint64]bool)
	collectedSet := make(map[uint64]bool)
	for _, record := range collectedRecords {
		collectedSet[uint64(record.BaccanId)] = true
	}

	for _, id := range bacaanIds {
		result[id] = collectedSet[id]
	}

	return result, nil
}

// GetHajiDoaRingkasList 获取朝觐祈祷文简要列表
func (s *sPrayer) GetHajiDoaRingkasList(ctx context.Context) ([]*model.HajiDoaRingkasInfo, error) {
	result, err := s.queryDoaData(ctx, "doa", "doa-haji-umrah", nil)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文数据失败:", err)
		return nil, err
	}

	if result.Category == nil {
		g.Log().Warning(ctx, "未找到朝觐祈祷文分类")
		return []*model.HajiDoaRingkasInfo{}, nil
	}

	if len(result.SubCategories) == 0 {
		return []*model.HajiDoaRingkasInfo{}, nil
	}

	// 构建返回数据
	var doaList []*model.HajiDoaRingkasInfo
	doaMap := make(map[int]*model.HajiDoaRingkasInfo)

	for _, subCat := range result.SubCategories {
		doaInfo := &model.HajiDoaRingkasInfo{
			Id:       uint64(subCat.Id),
			DoaNo:    subCat.OrderNumber,
			DoaName:  subCat.Name,
			Contents: []*model.HajiDoaRingkasContentInfo{},
		}
		doaList = append(doaList, doaInfo)
		doaMap[subCat.Id] = doaInfo
	}

	// 收集所有bacaan_id用于批量查询收藏状态
	var bacaanIds []uint64
	for _, doaData := range result.DoaDataList {
		bacaanIds = append(bacaanIds, uint64(doaData.Id))
	}

	// 批量查询收藏状态
	collectStatusMap, err := s.batchCheckDoaCollectStatus(ctx, bacaanIds)
	if err != nil {
		g.Log().Error(ctx, "批量查询收藏状态失败:", err)
		// 如果查询失败，继续处理，但收藏状态都设为false
		collectStatusMap = make(map[uint64]bool)
		for _, id := range bacaanIds {
			collectStatusMap[id] = false
		}
	}

	// 组装内容
	for _, doaData := range result.DoaDataList {
		if doa, exists := doaMap[doaData.CategoryId]; exists {
			title, arabicText, indonesianText, latinText := buildDoaContent(doaData)
			bacaanId := uint64(doaData.Id)
			contentInfo := &model.HajiDoaRingkasContentInfo{
				Id:             bacaanId,
				ContentOrder:   doaData.OrderNumber,
				Title:          title,
				MuqattaAt:      "",
				ArabicText:     arabicText,
				IndonesianText: indonesianText,
				LatinText:      latinText,
				ContentType:    doaData.Type,               // 对应doa_data的type字段
				BacaanId:       bacaanId,                   // 直接就是id
				IsCollected:    collectStatusMap[bacaanId], // 从批量查询结果中获取
			}
			doa.Contents = append(doa.Contents, contentInfo)
		}
	}

	return doaList, nil
}

// GetHajiDoaPanjangList 获取朝觐祈祷文详细列表
func (s *sPrayer) GetHajiDoaPanjangList(ctx context.Context) ([]*model.HajiDoaPanjangInfo, error) {
	var categories []*entity.DoaCategories
	err := dao.DoaCategories.Ctx(ctx).
		Fields("id, name, order_number, total").
		Where("group_name", "haji-dan-umrah").
		Order("id ASC").
		Scan(&categories)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文详细列表失败:", err)
		return nil, err
	}

	var doaList []*model.HajiDoaPanjangInfo
	for i, category := range categories {
		doaInfo := &model.HajiDoaPanjangInfo{
			Id:          uint64(category.Id),
			DoaNo:       i + 1,
			DoaName:     category.Name,
			BacaanCount: category.Total,
		}
		doaList = append(doaList, doaInfo)
	}

	return doaList, nil
}

// GetHajiDoaPanjangBacaanList 获取朝觐祈祷文诵读内容列表
func (s *sPrayer) GetHajiDoaPanjangBacaanList(ctx context.Context, doaId uint64) (*model.HajiDoaPanjangBacaanListOutput, error) {
	result, err := s.queryDoaData(ctx, "", "", &doaId)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐祈祷文数据失败:", err)
		return nil, err
	}

	if result.Category == nil {
		return nil, gerror.New("not found")
	}

	if len(result.SubCategories) == 0 {
		return &model.HajiDoaPanjangBacaanListOutput{
			List:    []*model.HajiDoaPanjangBacaanInfo{},
			DoaNo:   result.Category.OrderNumber,
			DoaName: result.Category.Name,
		}, nil
	}

	// 构建诵读列表
	var bacaanList []*model.HajiDoaPanjangBacaanInfo
	bacaanMap := make(map[int]*model.HajiDoaPanjangBacaanInfo)

	for _, subCat := range result.SubCategories {
		bacaanInfo := &model.HajiDoaPanjangBacaanInfo{
			Id:         uint64(subCat.Id),
			DoaId:      doaId,
			BacaanNo:   subCat.OrderNumber,
			BacaanName: subCat.Name,
			Contents:   []*model.HajiDoaPanjangBacaanContentInfo{},
		}
		bacaanList = append(bacaanList, bacaanInfo)
		bacaanMap[subCat.Id] = bacaanInfo
	}

	// 组装祈祷文内容
	for _, doaData := range result.DoaDataList {
		if bacaan, exists := bacaanMap[doaData.CategoryId]; exists {
			title, arabicText, indonesianText, latinText := buildDoaContent(doaData)
			contentInfo := &model.HajiDoaPanjangBacaanContentInfo{
				Id:             uint64(doaData.Id),
				ContentOrder:   doaData.OrderNumber,
				Title:          title,
				MuqattaAt:      "",
				ArabicText:     arabicText,
				IndonesianText: indonesianText,
				LatinText:      latinText,
			}
			bacaan.Contents = append(bacaan.Contents, contentInfo)
		}
	}

	return &model.HajiDoaPanjangBacaanListOutput{
		List:    bacaanList,
		DoaNo:   result.Category.OrderNumber,
		DoaName: result.Category.Name,
	}, nil
}

// GetHajiHikmahList 获取朝觐智慧列表
func (s *sPrayer) GetHajiHikmahList(ctx context.Context, languageId uint) (*model.HajiHikmahListOutput, error) {
	var hikmahList []*model.HajiHikmahInfo
	err := dao.HajiHikmah.Ctx(ctx).As("hh").
		Fields("hh.id, hh.article_id, hhl.title").
		LeftJoin(dao.HajiHikmahLanguages.Table()+" hhl", "hhl.hikmah_id = hh.id").
		Where("hhl.language_id", languageId).
		Order("hh.sort_order ASC, hh.id ASC").
		Scan(&hikmahList)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐智慧列表失败:", err)
		return nil, err
	}

	return &model.HajiHikmahListOutput{
		List: hikmahList,
	}, nil
}

// GetHajiNewsList 获取朝觐新闻列表
func (s *sPrayer) GetHajiNewsList(ctx context.Context, languageId uint, page, size int) (*model.HajiNewsListOutput, error) {
	headerTag := s.getHajiNewsHeaderTag(ctx, languageId)

	// 获取所有关联的 article_id
	articleIds, err := s.getHajiNewsArticleIds(ctx)
	if err != nil {
		return nil, err
	}

	if len(articleIds) == 0 {
		return &model.HajiNewsListOutput{
			List:      []*model.HajiNewsItem{},
			Total:     0,
			HeaderTag: headerTag,
		}, nil
	}

	// 先查询总数
	baseQuery := dao.NewsArticle.Ctx(ctx).As("na").
		LeftJoin(dao.NewsArticleLanguage.Table()+" nal", "nal.article_id = na.id").
		LeftJoin(dao.NewsCategoryLanguage.Table()+" ncl", "ncl.category_id = na.category_id").
		WhereIn("na.id", articleIds).
		Where("na.is_publish", 1).
		Where("nal.language_id", languageId).
		Where("ncl.language_id", languageId)

	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询朝觐新闻总数失败:", err)
		return nil, err
	}

	// 根据 article_id 查询文章详情，使用数据库分页
	type newsQueryResult struct {
		ArticleId    uint   `json:"article_id"`
		CategoryName string `json:"category_name"`
		Title        string `json:"title"`
		PublishTime  int64  `json:"publish_time"`
		CoverImage   string `json:"cover_image"`
	}

	var results []*newsQueryResult
	err = baseQuery.
		Fields("na.id as article_id, ncl.name as category_name, nal.name as title, na.publish_time, na.cover_imgs as cover_image").
		Order("na.publish_time DESC"). // 直接按发布时间降序排序
		Page(page, size).
		Scan(&results)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐新闻列表失败:", err)
		return nil, err
	}

	newsList := make([]*model.HajiNewsItem, 0, len(results))
	for _, result := range results {
		newsList = append(newsList, &model.HajiNewsItem{
			ArticleId:    uint64(result.ArticleId), // 类型转换：uint -> uint64
			CategoryName: result.CategoryName,
			Title:        result.Title,
			PublishTime:  result.PublishTime,
			CoverImage:   result.CoverImage,
		})
	}

	return &model.HajiNewsListOutput{
		List:      newsList,
		Total:     total,
		HeaderTag: headerTag,
	}, nil
}

// getHajiNewsArticleIds 获取朝觐新闻关联的所有文章ID
func (s *sPrayer) getHajiNewsArticleIds(ctx context.Context) ([]uint, error) {
	type articleIdResult struct {
		ArticleId uint `json:"article_id"`
	}
	var articleIdResults []articleIdResult
	err := dao.HajiNewsTagArticle.Ctx(ctx).
		Fields("DISTINCT article_id").
		Scan(&articleIdResults)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐新闻标签关联失败:", err)
		return nil, err
	}

	// 提取 article_id 到数组
	articleIds := make([]uint, len(articleIdResults))
	for i, result := range articleIdResults {
		articleIds[i] = result.ArticleId
	}
	return articleIds, nil
}

// getHajiNewsHeaderTag 获取朝觐新闻标签名（按sort_order最靠前的）
func (s *sPrayer) getHajiNewsHeaderTag(ctx context.Context, languageId uint) string {
	headerTagValue, err := dao.HajiNewsTag.Ctx(ctx).As("hnt").
		LeftJoin(dao.HajiNewsTagLanguages.Table()+" hntl", "hntl.tag_id = hnt.id").
		Where("hntl.language_id", languageId).
		Fields("hntl.tag_name").
		Order("hnt.sort_order ASC").
		Limit(1).
		Value()
	if err == nil && headerTagValue != nil {
		return headerTagValue.String()
	}
	if err != nil {
		g.Log().Warning(ctx, "查询朝觐新闻标签失败:", err)
	}
	// 不影响主要功能，使用默认值
	return ""
}

// GetHajiLandmarkList 获取朝觐地标列表（支持分页）
func (s *sPrayer) GetHajiLandmarkList(ctx context.Context, innerType string, languageId uint, page, size int) (*model.HajiLandmarkListOutput, error) {
	baseQuery := dao.HajiLandmark.Ctx(ctx).As("hl").
		LeftJoin(dao.HajiLandmarkLanguages.Table()+" hll", "hll.landmark_id = hl.id").
		LeftJoin(dao.HajiLandmarkType.Table()+" hlt", "hlt.id = hl.type_id").
		LeftJoin(dao.HajiLandmarkTypeLanguages.Table()+" hltl", "hltl.type_id = hl.type_id").
		Where("hll.language_id", languageId).
		Where("hltl.language_id", languageId)

	// 内部类型, destinasi(目的地), tokoh(人物)
	if innerType != "" {
		baseQuery = baseQuery.Where("hl.inner_type", innerType)
	}

	// 查询总数
	total, err := baseQuery.Count()
	if err != nil {
		g.Log().Error(ctx, "查询朝觐地标总数失败:", err)
		return nil, err
	}

	// 查询分页数据
	dataQuery := baseQuery.
		Fields("hl.id, hl.latitude, hl.longitude, hl.image_url, hll.landmark_name, hlt.icon_url, hltl.type_name").
		Order("hl.sort_order ASC, hl.id ASC").
		Page(page, size)

	type landmarkQueryResult struct {
		Id           uint64  `json:"id"`
		Latitude     float64 `json:"latitude"`
		Longitude    float64 `json:"longitude"`
		ImageUrl     string  `json:"image_url"`
		LandmarkName string  `json:"landmark_name"`
		IconUrl      string  `json:"icon_url"`
		TypeName     string  `json:"type_name"`
	}

	var results []*landmarkQueryResult
	err = dataQuery.Scan(&results)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐地标列表失败:", err)
		return nil, err
	}

	landmarkList := make([]*model.HajiLandmarkItem, 0, len(results))
	for _, result := range results {
		landmarkList = append(landmarkList, &model.HajiLandmarkItem{
			LandmarkId:   result.Id,
			Latitude:     result.Latitude,
			Longitude:    result.Longitude,
			ImageUrl:     result.ImageUrl,
			LandmarkName: result.LandmarkName,
			TypeIconUrl:  result.IconUrl,
			TypeName:     result.TypeName,
		})
	}

	return &model.HajiLandmarkListOutput{
		List:  landmarkList,
		Total: total,
	}, nil
}

// GetHajiLandmarkDetail 获取朝觐地标详情
func (s *sPrayer) GetHajiLandmarkDetail(ctx context.Context, landmarkId uint64, languageId uint) (*model.HajiLandmarkInfo, error) {
	type landmarkDetailResult struct {
		Id               uint64  `json:"id"`
		Latitude         float64 `json:"latitude"`
		Longitude        float64 `json:"longitude"`
		ImageUrl         string  `json:"image_url"`
		LandmarkName     string  `json:"landmark_name"`
		Country          string  `json:"country"`
		Address          string  `json:"address"`
		ShortDescription string  `json:"short_description"`
		InformationText  string  `json:"information_text"`
		IconUrl          string  `json:"icon_url"`
		TypeName         string  `json:"type_name"`
	}

	var result landmarkDetailResult
	err := dao.HajiLandmark.Ctx(ctx).As("hl").
		LeftJoin(dao.HajiLandmarkLanguages.Table()+" hll", "hll.landmark_id = hl.id").
		LeftJoin(dao.HajiLandmarkType.Table()+" hlt", "hlt.id = hl.type_id").
		LeftJoin(dao.HajiLandmarkTypeLanguages.Table()+" hltl", "hltl.type_id = hl.type_id").
		Where("hl.id", landmarkId).
		Where("hll.language_id", languageId).
		Where("hltl.language_id", languageId).
		Fields("hl.id, hl.latitude, hl.longitude, hl.image_url, hll.landmark_name, hll.country, hll.address, hll.short_description, hll.information_text, hlt.icon_url, hltl.type_name").
		Scan(&result)
	if err != nil {
		g.Log().Error(ctx, "查询朝觐地标详情失败:", err)
		return nil, err
	}

	if result.Id == 0 {
		return nil, gerror.New("data not found")
	}

	return &model.HajiLandmarkInfo{
		LandmarkId:       result.Id,
		Latitude:         result.Latitude,
		Longitude:        result.Longitude,
		ImageUrl:         result.ImageUrl,
		LandmarkName:     result.LandmarkName,
		Country:          result.Country,
		Address:          result.Address,
		ShortDescription: result.ShortDescription,
		InformationText:  result.InformationText,
		TypeIconUrl:      result.IconUrl,
		TypeName:         result.TypeName,
	}, nil
}

// GetRamadhanDoaList 获取斋月祈祷文简要列表
func (s *sPrayer) GetRamadhanDoaList(ctx context.Context) ([]*model.RamadhanDoaInfo, error) {
	result, err := s.queryDoaData(ctx, "spesial", "ramadhan", nil)
	if err != nil {
		g.Log().Error(ctx, "查询斋月祈祷文数据失败:", err)
		return nil, err
	}

	if result.Category == nil {
		g.Log().Warning(ctx, "未找到斋月祈祷文分类")
		return []*model.RamadhanDoaInfo{}, nil
	}

	if len(result.SubCategories) == 0 {
		return []*model.RamadhanDoaInfo{}, nil
	}

	// 构建返回数据
	var doaList []*model.RamadhanDoaInfo
	doaMap := make(map[int]*model.RamadhanDoaInfo)

	for _, subCat := range result.SubCategories {
		doaInfo := &model.RamadhanDoaInfo{
			Id:       uint64(subCat.Id),
			DoaNo:    subCat.OrderNumber,
			DoaName:  subCat.Name,
			Contents: []*model.RamadhanDoaContentInfo{},
		}
		doaList = append(doaList, doaInfo)
		doaMap[subCat.Id] = doaInfo
	}

	// 组装内容
	for _, doaData := range result.DoaDataList {
		if doa, exists := doaMap[doaData.CategoryId]; exists {
			title, arabicText, indonesianText, latinText := buildDoaContent(doaData)
			contentInfo := &model.RamadhanDoaContentInfo{
				Id:             uint64(doaData.Id),
				ContentOrder:   doaData.OrderNumber,
				Title:          title,
				MuqattaAt:      "",
				ArabicText:     arabicText,
				IndonesianText: indonesianText,
				LatinText:      latinText,
			}
			doa.Contents = append(doa.Contents, contentInfo)
		}
	}

	return doaList, nil
}
