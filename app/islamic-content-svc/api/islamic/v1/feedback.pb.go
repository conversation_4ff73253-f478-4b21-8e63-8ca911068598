// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/feedback.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FeedbackAddReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FeedbackType  int32                  `protobuf:"varint,1,opt,name=feedback_type,json=feedbackType,proto3" json:"feedback_type,omitempty" dc:"反馈类型 1-问题反馈,2-建议反馈"` // 反馈类型 1-问题反馈,2-建议反馈
	Desc          string                 `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty" dc:"反馈描述"`                                                    // 反馈描述
	Images        []*FeedBackImages      `protobuf:"bytes,3,rep,name=images,proto3" json:"images,omitempty" dc:"反馈图片"`                                                // 反馈图片
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeedbackAddReq) Reset() {
	*x = FeedbackAddReq{}
	mi := &file_islamic_v1_feedback_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeedbackAddReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackAddReq) ProtoMessage() {}

func (x *FeedbackAddReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_feedback_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackAddReq.ProtoReflect.Descriptor instead.
func (*FeedbackAddReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_feedback_proto_rawDescGZIP(), []int{0}
}

func (x *FeedbackAddReq) GetFeedbackType() int32 {
	if x != nil {
		return x.FeedbackType
	}
	return 0
}

func (x *FeedbackAddReq) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *FeedbackAddReq) GetImages() []*FeedBackImages {
	if x != nil {
		return x.Images
	}
	return nil
}

type FeedBackImages struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeedBackImages) Reset() {
	*x = FeedBackImages{}
	mi := &file_islamic_v1_feedback_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeedBackImages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedBackImages) ProtoMessage() {}

func (x *FeedBackImages) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_feedback_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedBackImages.ProtoReflect.Descriptor instead.
func (*FeedBackImages) Descriptor() ([]byte, []int) {
	return file_islamic_v1_feedback_proto_rawDescGZIP(), []int{1}
}

func (x *FeedBackImages) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FeedBackImages) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *FeedBackImages) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type FeedbackAddRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FeedbackAddRes) Reset() {
	*x = FeedbackAddRes{}
	mi := &file_islamic_v1_feedback_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FeedbackAddRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackAddRes) ProtoMessage() {}

func (x *FeedbackAddRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_feedback_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackAddRes.ProtoReflect.Descriptor instead.
func (*FeedbackAddRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_feedback_proto_rawDescGZIP(), []int{2}
}

func (x *FeedbackAddRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *FeedbackAddRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *FeedbackAddRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_islamic_v1_feedback_proto protoreflect.FileDescriptor

const file_islamic_v1_feedback_proto_rawDesc = "" +
	"\n" +
	"\x19islamic/v1/feedback.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\"}\n" +
	"\x0eFeedbackAddReq\x12#\n" +
	"\rfeedback_type\x18\x01 \x01(\x05R\ffeedbackType\x12\x12\n" +
	"\x04desc\x18\x02 \x01(\tR\x04desc\x122\n" +
	"\x06images\x18\x03 \x03(\v2\x1a.islamic.v1.FeedBackImagesR\x06images\"H\n" +
	"\x0eFeedBackImages\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"[\n" +
	"\x0eFeedbackAddRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error2X\n" +
	"\x0fFeedbackService\x12E\n" +
	"\vFeedbackAdd\x12\x1a.islamic.v1.FeedbackAddReq\x1a\x1a.islamic.v1.FeedbackAddResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_feedback_proto_rawDescOnce sync.Once
	file_islamic_v1_feedback_proto_rawDescData []byte
)

func file_islamic_v1_feedback_proto_rawDescGZIP() []byte {
	file_islamic_v1_feedback_proto_rawDescOnce.Do(func() {
		file_islamic_v1_feedback_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_feedback_proto_rawDesc), len(file_islamic_v1_feedback_proto_rawDesc)))
	})
	return file_islamic_v1_feedback_proto_rawDescData
}

var file_islamic_v1_feedback_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_islamic_v1_feedback_proto_goTypes = []any{
	(*FeedbackAddReq)(nil), // 0: islamic.v1.FeedbackAddReq
	(*FeedBackImages)(nil), // 1: islamic.v1.FeedBackImages
	(*FeedbackAddRes)(nil), // 2: islamic.v1.FeedbackAddRes
	(*common.Error)(nil),   // 3: common.Error
}
var file_islamic_v1_feedback_proto_depIdxs = []int32{
	1, // 0: islamic.v1.FeedbackAddReq.images:type_name -> islamic.v1.FeedBackImages
	3, // 1: islamic.v1.FeedbackAddRes.error:type_name -> common.Error
	0, // 2: islamic.v1.FeedbackService.FeedbackAdd:input_type -> islamic.v1.FeedbackAddReq
	2, // 3: islamic.v1.FeedbackService.FeedbackAdd:output_type -> islamic.v1.FeedbackAddRes
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_islamic_v1_feedback_proto_init() }
func file_islamic_v1_feedback_proto_init() {
	if File_islamic_v1_feedback_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_feedback_proto_rawDesc), len(file_islamic_v1_feedback_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_feedback_proto_goTypes,
		DependencyIndexes: file_islamic_v1_feedback_proto_depIdxs,
		MessageInfos:      file_islamic_v1_feedback_proto_msgTypes,
	}.Build()
	File_islamic_v1_feedback_proto = out.File
	file_islamic_v1_feedback_proto_goTypes = nil
	file_islamic_v1_feedback_proto_depIdxs = nil
}
