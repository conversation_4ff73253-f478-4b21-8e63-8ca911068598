// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/check_update.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CheckUpdateReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Platform       string                 `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty" dc:"平台"`                                     // 平台
	CurrentVersion string                 `protobuf:"bytes,2,opt,name=current_version,json=currentVersion,proto3" json:"current_version,omitempty" dc:"当前版本"` // 当前版本
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CheckUpdateReq) Reset() {
	*x = CheckUpdateReq{}
	mi := &file_islamic_v1_check_update_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateReq) ProtoMessage() {}

func (x *CheckUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_check_update_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateReq.ProtoReflect.Descriptor instead.
func (*CheckUpdateReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_check_update_proto_rawDescGZIP(), []int{0}
}

func (x *CheckUpdateReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *CheckUpdateReq) GetCurrentVersion() string {
	if x != nil {
		return x.CurrentVersion
	}
	return ""
}

type CheckUpdateRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *CheckUpdateData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpdateRes) Reset() {
	*x = CheckUpdateRes{}
	mi := &file_islamic_v1_check_update_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateRes) ProtoMessage() {}

func (x *CheckUpdateRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_check_update_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateRes.ProtoReflect.Descriptor instead.
func (*CheckUpdateRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_check_update_proto_rawDescGZIP(), []int{1}
}

func (x *CheckUpdateRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckUpdateRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckUpdateRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckUpdateRes) GetData() *CheckUpdateData {
	if x != nil {
		return x.Data
	}
	return nil
}

type CheckUpdateData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsUpdate      *bool                  `protobuf:"varint,1,opt,name=is_update,json=isUpdate,proto3,oneof" json:"is_update,omitempty" dc:"是否需要更新"`             // 是否需要更新
	ForceUpdate   *bool                  `protobuf:"varint,2,opt,name=force_update,json=forceUpdate,proto3,oneof" json:"force_update,omitempty" dc:"是否强制更新"`    // 是否强制更新
	LatestVersion *string                `protobuf:"bytes,3,opt,name=latest_version,json=latestVersion,proto3,oneof" json:"latest_version,omitempty" dc:"最新版本"` // 最新版本
	UpdateUrl     *string                `protobuf:"bytes,4,opt,name=update_url,json=updateUrl,proto3,oneof" json:"update_url,omitempty" dc:"下载地址"`             // 下载地址
	FileSize      *int32                 `protobuf:"varint,5,opt,name=file_size,json=fileSize,proto3,oneof" json:"file_size,omitempty" dc:"文件大小"`               // 文件大小
	ReleaseDate   *string                `protobuf:"bytes,6,opt,name=release_date,json=releaseDate,proto3,oneof" json:"release_date,omitempty" dc:"发布时间"`       // 发布时间
	Md5           *string                `protobuf:"bytes,7,opt,name=md5,proto3,oneof" json:"md5,omitempty" dc:"文件md5"`                                         // 文件md5
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUpdateData) Reset() {
	*x = CheckUpdateData{}
	mi := &file_islamic_v1_check_update_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUpdateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUpdateData) ProtoMessage() {}

func (x *CheckUpdateData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_check_update_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUpdateData.ProtoReflect.Descriptor instead.
func (*CheckUpdateData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_check_update_proto_rawDescGZIP(), []int{2}
}

func (x *CheckUpdateData) GetIsUpdate() bool {
	if x != nil && x.IsUpdate != nil {
		return *x.IsUpdate
	}
	return false
}

func (x *CheckUpdateData) GetForceUpdate() bool {
	if x != nil && x.ForceUpdate != nil {
		return *x.ForceUpdate
	}
	return false
}

func (x *CheckUpdateData) GetLatestVersion() string {
	if x != nil && x.LatestVersion != nil {
		return *x.LatestVersion
	}
	return ""
}

func (x *CheckUpdateData) GetUpdateUrl() string {
	if x != nil && x.UpdateUrl != nil {
		return *x.UpdateUrl
	}
	return ""
}

func (x *CheckUpdateData) GetFileSize() int32 {
	if x != nil && x.FileSize != nil {
		return *x.FileSize
	}
	return 0
}

func (x *CheckUpdateData) GetReleaseDate() string {
	if x != nil && x.ReleaseDate != nil {
		return *x.ReleaseDate
	}
	return ""
}

func (x *CheckUpdateData) GetMd5() string {
	if x != nil && x.Md5 != nil {
		return *x.Md5
	}
	return ""
}

var File_islamic_v1_check_update_proto protoreflect.FileDescriptor

const file_islamic_v1_check_update_proto_rawDesc = "" +
	"\n" +
	"\x1dislamic/v1/check_update.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\"U\n" +
	"\x0eCheckUpdateReq\x12\x1a\n" +
	"\bplatform\x18\x01 \x01(\tR\bplatform\x12'\n" +
	"\x0fcurrent_version\x18\x02 \x01(\tR\x0ecurrentVersion\"\x8c\x01\n" +
	"\x0eCheckUpdateRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.islamic.v1.CheckUpdateDataR\x04data\"\xf4\x02\n" +
	"\x0fCheckUpdateData\x12 \n" +
	"\tis_update\x18\x01 \x01(\bH\x00R\bisUpdate\x88\x01\x01\x12&\n" +
	"\fforce_update\x18\x02 \x01(\bH\x01R\vforceUpdate\x88\x01\x01\x12*\n" +
	"\x0elatest_version\x18\x03 \x01(\tH\x02R\rlatestVersion\x88\x01\x01\x12\"\n" +
	"\n" +
	"update_url\x18\x04 \x01(\tH\x03R\tupdateUrl\x88\x01\x01\x12 \n" +
	"\tfile_size\x18\x05 \x01(\x05H\x04R\bfileSize\x88\x01\x01\x12&\n" +
	"\frelease_date\x18\x06 \x01(\tH\x05R\vreleaseDate\x88\x01\x01\x12\x15\n" +
	"\x03md5\x18\a \x01(\tH\x06R\x03md5\x88\x01\x01B\f\n" +
	"\n" +
	"_is_updateB\x0f\n" +
	"\r_force_updateB\x11\n" +
	"\x0f_latest_versionB\r\n" +
	"\v_update_urlB\f\n" +
	"\n" +
	"_file_sizeB\x0f\n" +
	"\r_release_dateB\x06\n" +
	"\x04_md52[\n" +
	"\x12CheckUpdateService\x12E\n" +
	"\vCheckUpdate\x12\x1a.islamic.v1.CheckUpdateReq\x1a\x1a.islamic.v1.CheckUpdateResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_check_update_proto_rawDescOnce sync.Once
	file_islamic_v1_check_update_proto_rawDescData []byte
)

func file_islamic_v1_check_update_proto_rawDescGZIP() []byte {
	file_islamic_v1_check_update_proto_rawDescOnce.Do(func() {
		file_islamic_v1_check_update_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_check_update_proto_rawDesc), len(file_islamic_v1_check_update_proto_rawDesc)))
	})
	return file_islamic_v1_check_update_proto_rawDescData
}

var file_islamic_v1_check_update_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_islamic_v1_check_update_proto_goTypes = []any{
	(*CheckUpdateReq)(nil),  // 0: islamic.v1.CheckUpdateReq
	(*CheckUpdateRes)(nil),  // 1: islamic.v1.CheckUpdateRes
	(*CheckUpdateData)(nil), // 2: islamic.v1.CheckUpdateData
	(*common.Error)(nil),    // 3: common.Error
}
var file_islamic_v1_check_update_proto_depIdxs = []int32{
	3, // 0: islamic.v1.CheckUpdateRes.error:type_name -> common.Error
	2, // 1: islamic.v1.CheckUpdateRes.data:type_name -> islamic.v1.CheckUpdateData
	0, // 2: islamic.v1.CheckUpdateService.CheckUpdate:input_type -> islamic.v1.CheckUpdateReq
	1, // 3: islamic.v1.CheckUpdateService.CheckUpdate:output_type -> islamic.v1.CheckUpdateRes
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_islamic_v1_check_update_proto_init() }
func file_islamic_v1_check_update_proto_init() {
	if File_islamic_v1_check_update_proto != nil {
		return
	}
	file_islamic_v1_check_update_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_check_update_proto_rawDesc), len(file_islamic_v1_check_update_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_check_update_proto_goTypes,
		DependencyIndexes: file_islamic_v1_check_update_proto_depIdxs,
		MessageInfos:      file_islamic_v1_check_update_proto_msgTypes,
	}.Build()
	File_islamic_v1_check_update_proto = out.File
	file_islamic_v1_check_update_proto_goTypes = nil
	file_islamic_v1_check_update_proto_depIdxs = nil
}
