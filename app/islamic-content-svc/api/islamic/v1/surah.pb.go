// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/surah.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 古兰经-章-列表
type SurahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"章节id"`                                // 章节id
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                               //名称
	IsPopular     uint32                 `protobuf:"varint,3,opt,name=is_popular,json=isPopular,proto3" json:"is_popular,omitempty" dc:"是否热门"` // 是否热门
	Page          *common.PageRequest    `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                             // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListReq) Reset() {
	*x = SurahListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListReq) ProtoMessage() {}

func (x *SurahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListReq.ProtoReflect.Descriptor instead.
func (*SurahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{0}
}

func (x *SurahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurahListReq) GetIsPopular() uint32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

func (x *SurahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type SuratDaftarInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Nomor         uint32                 `protobuf:"varint,2,opt,name=Nomor,proto3" json:"Nomor,omitempty"`
	Nama          string                 `protobuf:"bytes,3,opt,name=Nama,proto3" json:"Nama,omitempty"`
	NamaLatin     string                 `protobuf:"bytes,4,opt,name=NamaLatin,proto3" json:"NamaLatin,omitempty"`
	JumlahAyat    uint32                 `protobuf:"varint,5,opt,name=JumlahAyat,proto3" json:"JumlahAyat,omitempty"`
	TempatTurun   string                 `protobuf:"bytes,6,opt,name=TempatTurun,proto3" json:"TempatTurun,omitempty"`
	Arti          string                 `protobuf:"bytes,7,opt,name=Arti,proto3" json:"Arti,omitempty"`
	Deskripsi     string                 `protobuf:"bytes,8,opt,name=Deskripsi,proto3" json:"Deskripsi,omitempty"`
	Audio         string                 `protobuf:"bytes,9,opt,name=Audio,proto3" json:"Audio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuratDaftarInfo) Reset() {
	*x = SuratDaftarInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuratDaftarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratDaftarInfo) ProtoMessage() {}

func (x *SuratDaftarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratDaftarInfo.ProtoReflect.Descriptor instead.
func (*SuratDaftarInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{1}
}

func (x *SuratDaftarInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratDaftarInfo) GetNomor() uint32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratDaftarInfo) GetNama() string {
	if x != nil {
		return x.Nama
	}
	return ""
}

func (x *SuratDaftarInfo) GetNamaLatin() string {
	if x != nil {
		return x.NamaLatin
	}
	return ""
}

func (x *SuratDaftarInfo) GetJumlahAyat() uint32 {
	if x != nil {
		return x.JumlahAyat
	}
	return 0
}

func (x *SuratDaftarInfo) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

func (x *SuratDaftarInfo) GetArti() string {
	if x != nil {
		return x.Arti
	}
	return ""
}

func (x *SuratDaftarInfo) GetDeskripsi() string {
	if x != nil {
		return x.Deskripsi
	}
	return ""
}

func (x *SuratDaftarInfo) GetAudio() string {
	if x != nil {
		return x.Audio
	}
	return ""
}

type SurahListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*SuratDaftarInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListResData) Reset() {
	*x = SurahListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListResData) ProtoMessage() {}

func (x *SurahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListResData.ProtoReflect.Descriptor instead.
func (*SurahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{2}
}

func (x *SurahListResData) GetList() []*SuratDaftarInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type SurahListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *SurahListResData      `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListRes) Reset() {
	*x = SurahListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListRes) ProtoMessage() {}

func (x *SurahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListRes.ProtoReflect.Descriptor instead.
func (*SurahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{3}
}

func (x *SurahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahListRes) GetData() *SurahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type JuzListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" dc:"juz名称"` // juz名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListReq) Reset() {
	*x = JuzListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListReq) ProtoMessage() {}

func (x *JuzListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListReq.ProtoReflect.Descriptor instead.
func (*JuzListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{4}
}

func (x *JuzListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type JuzInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StartSurahId   uint32                 `protobuf:"varint,1,opt,name=start_surah_id,json=startSurahId,proto3" json:"start_surah_id,omitempty" dc:"开始章id"`        // 开始章id
	StartSurahName string                 `protobuf:"bytes,2,opt,name=start_surah_name,json=startSurahName,proto3" json:"start_surah_name,omitempty" dc:"开始章name"` // 开始章name
	EndSurahId     uint32                 `protobuf:"varint,3,opt,name=end_surah_id,json=endSurahId,proto3" json:"end_surah_id,omitempty" dc:"结束章id"`              // 结束章id
	EndSurahName   string                 `protobuf:"bytes,4,opt,name=end_surah_name,json=endSurahName,proto3" json:"end_surah_name,omitempty" dc:"结束章name"`       // 结束章name
	StartAyahId    uint32                 `protobuf:"varint,5,opt,name=start_ayah_id,json=startAyahId,proto3" json:"start_ayah_id,omitempty" dc:"开始节id"`           // 开始节id
	EndAyahId      uint32                 `protobuf:"varint,6,opt,name=end_ayah_id,json=endAyahId,proto3" json:"end_ayah_id,omitempty" dc:"结束节id"`                 // 结束节id
	Juz            string                 `protobuf:"bytes,7,opt,name=juz,proto3" json:"juz,omitempty" dc:"juz名称"`                                                 // juz名称
	FirstWord      string                 `protobuf:"bytes,8,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`               // 对应经文的第一个单词
	Sort           uint32                 `protobuf:"varint,9,opt,name=sort,proto3" json:"sort,omitempty" dc:"对应经文的第一个单词"`                                         // 对应经文的第一个单词
	TempatTurun    string                 `protobuf:"bytes,10,opt,name=tempat_turun,json=tempatTurun,proto3" json:"tempat_turun,omitempty" dc:"朝拜方向"`              // 朝拜方向
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *JuzInfo) Reset() {
	*x = JuzInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzInfo) ProtoMessage() {}

func (x *JuzInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzInfo.ProtoReflect.Descriptor instead.
func (*JuzInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{5}
}

func (x *JuzInfo) GetStartSurahId() uint32 {
	if x != nil {
		return x.StartSurahId
	}
	return 0
}

func (x *JuzInfo) GetStartSurahName() string {
	if x != nil {
		return x.StartSurahName
	}
	return ""
}

func (x *JuzInfo) GetEndSurahId() uint32 {
	if x != nil {
		return x.EndSurahId
	}
	return 0
}

func (x *JuzInfo) GetEndSurahName() string {
	if x != nil {
		return x.EndSurahName
	}
	return ""
}

func (x *JuzInfo) GetStartAyahId() uint32 {
	if x != nil {
		return x.StartAyahId
	}
	return 0
}

func (x *JuzInfo) GetEndAyahId() uint32 {
	if x != nil {
		return x.EndAyahId
	}
	return 0
}

func (x *JuzInfo) GetJuz() string {
	if x != nil {
		return x.Juz
	}
	return ""
}

func (x *JuzInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

func (x *JuzInfo) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *JuzInfo) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

type JuzListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*JuzInfo             `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListResData) Reset() {
	*x = JuzListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListResData) ProtoMessage() {}

func (x *JuzListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListResData.ProtoReflect.Descriptor instead.
func (*JuzListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{6}
}

func (x *JuzListResData) GetList() []*JuzInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type JuzListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *JuzListResData        `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListRes) Reset() {
	*x = JuzListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListRes) ProtoMessage() {}

func (x *JuzListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListRes.ProtoReflect.Descriptor instead.
func (*JuzListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{7}
}

func (x *JuzListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JuzListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *JuzListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *JuzListRes) GetData() *JuzListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type AyahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`                                        // 节id
	SurahId       uint32                 `protobuf:"varint,2,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章节id"`              //章节id
	JuzId         uint32                 `protobuf:"varint,3,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz_id"`                  //juz_id
	PageNumber    uint32                 `protobuf:"varint,4,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty" dc:"page 页数量"` //page 页数量
	Page          *common.PageRequest    `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                    // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahListReq) Reset() {
	*x = AyahListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListReq) ProtoMessage() {}

func (x *AyahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListReq.ProtoReflect.Descriptor instead.
func (*AyahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{8}
}

func (x *AyahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AyahListReq) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *AyahListReq) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *AyahListReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *AyahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type SuratAyatInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"Ayat id"`                // Ayat id
	AyatId        uint32                 `protobuf:"varint,2,opt,name=AyatId,proto3" json:"AyatId,omitempty" dc:"Ayat id"`        // Ayat id
	SurahId       uint32                 `protobuf:"varint,3,opt,name=SurahId,proto3" json:"SurahId,omitempty" dc:"章 id"`         // 章 id
	Nomor         uint32                 `protobuf:"varint,4,opt,name=Nomor,proto3" json:"Nomor,omitempty" dc:"章id"`              // 章id
	Tr            string                 `protobuf:"bytes,5,opt,name=Tr,proto3" json:"Tr,omitempty" dc:"音译文本"`                    // 音译文本
	Idn           string                 `protobuf:"bytes,6,opt,name=Idn,proto3" json:"Idn,omitempty" dc:"印尼语翻译"`                 // 印尼语翻译
	Ar            string                 `protobuf:"bytes,7,opt,name=Ar,proto3" json:"Ar,omitempty" dc:"阿拉伯语经文"`                  // 阿拉伯语经文
	Tafsir        string                 `protobuf:"bytes,8,opt,name=Tafsir,proto3" json:"Tafsir,omitempty" dc:"解释"`              // 解释
	Juz           uint32                 `protobuf:"varint,9,opt,name=Juz,proto3" json:"Juz,omitempty" dc:"所属juz-id"`             // 所属juz-id
	Page          uint32                 `protobuf:"varint,10,opt,name=Page,proto3" json:"Page,omitempty" dc:"所属page-id"`         // 所属page-id
	SurahName     string                 `protobuf:"bytes,11,opt,name=SurahName,proto3" json:"SurahName,omitempty" dc:"章名称"`      // 章名称
	Wajiz         string                 `protobuf:"bytes,12,opt,name=Wajiz,proto3" json:"Wajiz,omitempty" dc:"解释"`               // 解释
	TempatTurun   string                 `protobuf:"bytes,13,opt,name=TempatTurun,proto3" json:"TempatTurun,omitempty" dc:"朝拜方向"` // 朝拜方向
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuratAyatInfo) Reset() {
	*x = SuratAyatInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuratAyatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratAyatInfo) ProtoMessage() {}

func (x *SuratAyatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratAyatInfo.ProtoReflect.Descriptor instead.
func (*SuratAyatInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{9}
}

func (x *SuratAyatInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratAyatInfo) GetAyatId() uint32 {
	if x != nil {
		return x.AyatId
	}
	return 0
}

func (x *SuratAyatInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *SuratAyatInfo) GetNomor() uint32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratAyatInfo) GetTr() string {
	if x != nil {
		return x.Tr
	}
	return ""
}

func (x *SuratAyatInfo) GetIdn() string {
	if x != nil {
		return x.Idn
	}
	return ""
}

func (x *SuratAyatInfo) GetAr() string {
	if x != nil {
		return x.Ar
	}
	return ""
}

func (x *SuratAyatInfo) GetTafsir() string {
	if x != nil {
		return x.Tafsir
	}
	return ""
}

func (x *SuratAyatInfo) GetJuz() uint32 {
	if x != nil {
		return x.Juz
	}
	return 0
}

func (x *SuratAyatInfo) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SuratAyatInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *SuratAyatInfo) GetWajiz() string {
	if x != nil {
		return x.Wajiz
	}
	return ""
}

func (x *SuratAyatInfo) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

type AyahListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*SuratAyatInfo       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahListResData) Reset() {
	*x = AyahListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListResData) ProtoMessage() {}

func (x *AyahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListResData.ProtoReflect.Descriptor instead.
func (*AyahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{10}
}

func (x *AyahListResData) GetList() []*SuratAyatInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *AyahListResData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahListRes) Reset() {
	*x = AyahListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListRes) ProtoMessage() {}

func (x *AyahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListRes.ProtoReflect.Descriptor instead.
func (*AyahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{11}
}

func (x *AyahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahListRes) GetData() *AyahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AyahId        uint32                 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                    //节id
	IsUserOp      uint32                 `protobuf:"varint,2,opt,name=is_user_op,json=isUserOp,proto3" json:"is_user_op,omitempty" dc:"是否用户操作，1-是，0-否"` //是否用户操作，1-是，0-否
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordReq) Reset() {
	*x = AyahReadRecordReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordReq) ProtoMessage() {}

func (x *AyahReadRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{12}
}

func (x *AyahReadRecordReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadRecordReq) GetIsUserOp() uint32 {
	if x != nil {
		return x.IsUserOp
	}
	return 0
}

type AyahReadRecordRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordRes) Reset() {
	*x = AyahReadRecordRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordRes) ProtoMessage() {}

func (x *AyahReadRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{13}
}

func (x *AyahReadRecordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AyahReadCollectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AyahId        uint32                 `protobuf:"varint,2,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"`            //章节id
	IsAdd         uint32                 `protobuf:"varint,3,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` //是否添加收藏，1-添加，0-取消收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectReq) Reset() {
	*x = AyahReadCollectReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectReq) ProtoMessage() {}

func (x *AyahReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{14}
}

func (x *AyahReadCollectReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

type AyahReadCollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectRes) Reset() {
	*x = AyahReadCollectRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectRes) ProtoMessage() {}

func (x *AyahReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{15}
}

func (x *AyahReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckAyahReadCollectStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AyahId        uint32                 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"` //章节id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckAyahReadCollectStatusReq) Reset() {
	*x = CheckAyahReadCollectStatusReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAyahReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusReq) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{16}
}

func (x *CheckAyahReadCollectStatusReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

type CheckAyahReadCollectStatusResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsCollect     int32                  `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckAyahReadCollectStatusResData) Reset() {
	*x = CheckAyahReadCollectStatusResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAyahReadCollectStatusResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusResData) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusResData.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{17}
}

func (x *CheckAyahReadCollectStatusResData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type CheckAyahReadCollectStatusRes struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Code          int32                              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *CheckAyahReadCollectStatusResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckAyahReadCollectStatusRes) Reset() {
	*x = CheckAyahReadCollectStatusRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAyahReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusRes) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{18}
}

func (x *CheckAyahReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckAyahReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckAyahReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckAyahReadCollectStatusRes) GetData() *CheckAyahReadCollectStatusResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ReadInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SurahId       uint32                 `protobuf:"varint,1,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章id"`               // 章id
	SurahName     string                 `protobuf:"bytes,2,opt,name=surah_name,json=surahName,proto3" json:"surah_name,omitempty" dc:"章name"`        // 章name
	AyahId        uint32                 `protobuf:"varint,3,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                  // 节id
	JuzId         uint32                 `protobuf:"varint,4,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz-id"`                  // juz-id
	Arti          string                 `protobuf:"bytes,5,opt,name=arti,proto3" json:"arti,omitempty" dc:"章节含义"`                                    // 章节含义
	Ayahs         uint32                 `protobuf:"varint,6,opt,name=ayahs,proto3" json:"ayahs,omitempty" dc:"ayah数量"`                               // ayah数量
	FirstWord     string                 `protobuf:"bytes,7,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`   // 对应经文的第一个单词
	Ar            string                 `protobuf:"bytes,8,opt,name=ar,proto3" json:"ar,omitempty" dc:"阿拉伯语经文"`                                      // 阿拉伯语经文
	Tr            string                 `protobuf:"bytes,9,opt,name=tr,proto3" json:"tr,omitempty" dc:"音译文本"`                                        // 音译文本
	Idn           string                 `protobuf:"bytes,10,opt,name=idn,proto3" json:"idn,omitempty" dc:"印尼语翻译"`                                    // 印尼语翻译
	CollectTime   int64                  `protobuf:"varint,11,opt,name=collect_time,json=collectTime,proto3" json:"collect_time,omitempty" dc:"收藏时间"` // 收藏时间
	Nomor         uint32                 `protobuf:"varint,12,opt,name=nomor,proto3" json:"nomor,omitempty" dc:"经文在章节中的编号"`                           // 经文在章节中的编号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadInfo) Reset() {
	*x = ReadInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInfo) ProtoMessage() {}

func (x *ReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInfo.ProtoReflect.Descriptor instead.
func (*ReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{19}
}

func (x *ReadInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *ReadInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *ReadInfo) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *ReadInfo) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *ReadInfo) GetArti() string {
	if x != nil {
		return x.Arti
	}
	return ""
}

func (x *ReadInfo) GetAyahs() uint32 {
	if x != nil {
		return x.Ayahs
	}
	return 0
}

func (x *ReadInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

func (x *ReadInfo) GetAr() string {
	if x != nil {
		return x.Ar
	}
	return ""
}

func (x *ReadInfo) GetTr() string {
	if x != nil {
		return x.Tr
	}
	return ""
}

func (x *ReadInfo) GetIdn() string {
	if x != nil {
		return x.Idn
	}
	return ""
}

func (x *ReadInfo) GetCollectTime() int64 {
	if x != nil {
		return x.CollectTime
	}
	return 0
}

func (x *ReadInfo) GetNomor() uint32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

type AyahReadRecordListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordListReq) Reset() {
	*x = AyahReadRecordListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListReq) ProtoMessage() {}

func (x *AyahReadRecordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{20}
}

func (x *AyahReadRecordListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ReadInfo            `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordListResData) Reset() {
	*x = AyahReadRecordListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListResData) ProtoMessage() {}

func (x *AyahReadRecordListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListResData.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{21}
}

func (x *AyahReadRecordListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadRecordListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListRes struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *AyahReadRecordListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordListRes) Reset() {
	*x = AyahReadRecordListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListRes) ProtoMessage() {}

func (x *AyahReadRecordListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{22}
}

func (x *AyahReadRecordListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadRecordListRes) GetData() *AyahReadRecordListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadCollectListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectListReq) Reset() {
	*x = AyahReadCollectListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListReq) ProtoMessage() {}

func (x *AyahReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{23}
}

func (x *AyahReadCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ReadInfo            `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectListResData) Reset() {
	*x = AyahReadCollectListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListResData) ProtoMessage() {}

func (x *AyahReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListResData.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{24}
}

func (x *AyahReadCollectListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListRes struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *AyahReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectListRes) Reset() {
	*x = AyahReadCollectListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListRes) ProtoMessage() {}

func (x *AyahReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{25}
}

func (x *AyahReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadCollectListRes) GetData() *AyahReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type TahlilListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TahlilListReq) Reset() {
	*x = TahlilListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TahlilListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListReq) ProtoMessage() {}

func (x *TahlilListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListReq.ProtoReflect.Descriptor instead.
func (*TahlilListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{26}
}

func (x *TahlilListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsTahlilInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"章id"`              // 章id
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"章name"`         // 章name
	Content1      string                 `protobuf:"bytes,3,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"章name"` // 章name
	Content2      string                 `protobuf:"bytes,4,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"章name"` // 章name
	Content3      string                 `protobuf:"bytes,5,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"章name"` // 章name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsTahlilInfo) Reset() {
	*x = NewsTahlilInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsTahlilInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTahlilInfo) ProtoMessage() {}

func (x *NewsTahlilInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTahlilInfo.ProtoReflect.Descriptor instead.
func (*NewsTahlilInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{27}
}

func (x *NewsTahlilInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsTahlilInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NewsTahlilInfo) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *NewsTahlilInfo) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *NewsTahlilInfo) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

type TahlilListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*NewsTahlilInfo      `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TahlilListResData) Reset() {
	*x = TahlilListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TahlilListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListResData) ProtoMessage() {}

func (x *TahlilListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListResData.ProtoReflect.Descriptor instead.
func (*TahlilListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{28}
}

func (x *TahlilListResData) GetList() []*NewsTahlilInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TahlilListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type TahlilListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *TahlilListResData     `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TahlilListRes) Reset() {
	*x = TahlilListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TahlilListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TahlilListRes) ProtoMessage() {}

func (x *TahlilListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TahlilListRes.ProtoReflect.Descriptor instead.
func (*TahlilListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{29}
}

func (x *TahlilListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TahlilListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *TahlilListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *TahlilListRes) GetData() *TahlilListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type WiridListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"` // name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridListReq) Reset() {
	*x = WiridListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridListReq) ProtoMessage() {}

func (x *WiridListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridListReq.ProtoReflect.Descriptor instead.
func (*WiridListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{30}
}

func (x *WiridListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *WiridListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WiridInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                  // id
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`             // name
	Bacaans       uint32                 `protobuf:"varint,3,opt,name=Bacaans,proto3" json:"Bacaans,omitempty" dc:"Bacaans数量"` // Bacaans数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridInfo) Reset() {
	*x = WiridInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridInfo) ProtoMessage() {}

func (x *WiridInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridInfo.ProtoReflect.Descriptor instead.
func (*WiridInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{31}
}

func (x *WiridInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WiridInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WiridInfo) GetBacaans() uint32 {
	if x != nil {
		return x.Bacaans
	}
	return 0
}

type WiridListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*WiridInfo           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridListResData) Reset() {
	*x = WiridListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridListResData) ProtoMessage() {}

func (x *WiridListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridListResData.ProtoReflect.Descriptor instead.
func (*WiridListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{32}
}

func (x *WiridListResData) GetList() []*WiridInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WiridListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type WiridListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *WiridListResData      `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridListRes) Reset() {
	*x = WiridListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridListRes) ProtoMessage() {}

func (x *WiridListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridListRes.ProtoReflect.Descriptor instead.
func (*WiridListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{33}
}

func (x *WiridListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WiridListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WiridListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WiridListRes) GetData() *WiridListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanList信息列表
type WiridBacaanListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	WiridId       int32                  `protobuf:"varint,2,opt,name=wirid_id,json=wiridId,proto3" json:"wirid_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"名称"` // 名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanListReq) Reset() {
	*x = WiridBacaanListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanListReq) ProtoMessage() {}

func (x *WiridBacaanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanListReq.ProtoReflect.Descriptor instead.
func (*WiridBacaanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{34}
}

func (x *WiridBacaanListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *WiridBacaanListReq) GetWiridId() int32 {
	if x != nil {
		return x.WiridId
	}
	return 0
}

func (x *WiridBacaanListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WiridBacaanList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Number        uint32                 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty" dc:"id"`     // id
	BacaanId      uint32                 `protobuf:"varint,2,opt,name=bacaanId,proto3" json:"bacaanId,omitempty" dc:"id"` // id
	Pid           uint32                 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty" dc:"父id"`          // 父id
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`          // 名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanList) Reset() {
	*x = WiridBacaanList{}
	mi := &file_islamic_v1_surah_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanList) ProtoMessage() {}

func (x *WiridBacaanList) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanList.ProtoReflect.Descriptor instead.
func (*WiridBacaanList) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{35}
}

func (x *WiridBacaanList) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *WiridBacaanList) GetBacaanId() uint32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *WiridBacaanList) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *WiridBacaanList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type WiridBacaanListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*WiridBacaanList     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanListResData) Reset() {
	*x = WiridBacaanListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanListResData) ProtoMessage() {}

func (x *WiridBacaanListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanListResData.ProtoReflect.Descriptor instead.
func (*WiridBacaanListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{36}
}

func (x *WiridBacaanListResData) GetList() []*WiridBacaanList {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WiridBacaanListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type WiridBacaanListRes struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Code          int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *WiridBacaanListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanListRes) Reset() {
	*x = WiridBacaanListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanListRes) ProtoMessage() {}

func (x *WiridBacaanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanListRes.ProtoReflect.Descriptor instead.
func (*WiridBacaanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{37}
}

func (x *WiridBacaanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WiridBacaanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WiridBacaanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WiridBacaanListRes) GetData() *WiridBacaanListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanInfo 信息列表
type WiridBacaanInfoListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	BacaanId      int32                  `protobuf:"varint,2,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanInfoListReq) Reset() {
	*x = WiridBacaanInfoListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanInfoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfoListReq) ProtoMessage() {}

func (x *WiridBacaanInfoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfoListReq.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{38}
}

func (x *WiridBacaanInfoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *WiridBacaanInfoListReq) GetBacaanId() int32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

type WiridBacaanInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                // id
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`           // name
	Content1      string                 `protobuf:"bytes,3,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"page参数"` // page参数
	Content2      string                 `protobuf:"bytes,4,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"经文"`     // 经文
	Content3      string                 `protobuf:"bytes,5,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"解释"`     // 解释
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanInfo) Reset() {
	*x = WiridBacaanInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfo) ProtoMessage() {}

func (x *WiridBacaanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfo.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{39}
}

func (x *WiridBacaanInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WiridBacaanInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WiridBacaanInfo) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *WiridBacaanInfo) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *WiridBacaanInfo) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

type WiridBacaanInfoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*WiridBacaanInfo     `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanInfoListResData) Reset() {
	*x = WiridBacaanInfoListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanInfoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfoListResData) ProtoMessage() {}

func (x *WiridBacaanInfoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfoListResData.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{40}
}

func (x *WiridBacaanInfoListResData) GetList() []*WiridBacaanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *WiridBacaanInfoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type WiridBacaanInfoListRes struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *WiridBacaanInfoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiridBacaanInfoListRes) Reset() {
	*x = WiridBacaanInfoListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiridBacaanInfoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiridBacaanInfoListRes) ProtoMessage() {}

func (x *WiridBacaanInfoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiridBacaanInfoListRes.ProtoReflect.Descriptor instead.
func (*WiridBacaanInfoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{41}
}

func (x *WiridBacaanInfoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *WiridBacaanInfoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *WiridBacaanInfoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *WiridBacaanInfoListRes) GetData() *WiridBacaanInfoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DoaListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"` // name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaListReq) Reset() {
	*x = DoaListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaListReq) ProtoMessage() {}

func (x *DoaListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaListReq.ProtoReflect.Descriptor instead.
func (*DoaListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{42}
}

func (x *DoaListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                  // id
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`             // name
	Bacaans       uint32                 `protobuf:"varint,3,opt,name=Bacaans,proto3" json:"Bacaans,omitempty" dc:"Bacaans数量"` // Bacaans数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaInfo) Reset() {
	*x = DoaInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaInfo) ProtoMessage() {}

func (x *DoaInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaInfo.ProtoReflect.Descriptor instead.
func (*DoaInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{43}
}

func (x *DoaInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaInfo) GetBacaans() uint32 {
	if x != nil {
		return x.Bacaans
	}
	return 0
}

type DoaListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DoaInfo             `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaListResData) Reset() {
	*x = DoaListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaListResData) ProtoMessage() {}

func (x *DoaListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaListResData.ProtoReflect.Descriptor instead.
func (*DoaListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{44}
}

func (x *DoaListResData) GetList() []*DoaInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *DoaListResData        `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaListRes) Reset() {
	*x = DoaListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaListRes) ProtoMessage() {}

func (x *DoaListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaListRes.ProtoReflect.Descriptor instead.
func (*DoaListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{45}
}

func (x *DoaListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaListRes) GetData() *DoaListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanList信息列表
type DoaBacaanListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	DoaId         int32                  `protobuf:"varint,2,opt,name=doa_id,json=doaId,proto3" json:"doa_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"名称"` // 名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanListReq) Reset() {
	*x = DoaBacaanListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanListReq) ProtoMessage() {}

func (x *DoaBacaanListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanListReq.ProtoReflect.Descriptor instead.
func (*DoaBacaanListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{46}
}

func (x *DoaBacaanListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaBacaanListReq) GetDoaId() int32 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

func (x *DoaBacaanListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaBacaanList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Number        uint32                 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty" dc:"id"`     // id
	BacaanId      uint32                 `protobuf:"varint,2,opt,name=bacaanId,proto3" json:"bacaanId,omitempty" dc:"id"` // id
	Pid           uint32                 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty" dc:"父id"`          // 父id
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`          // 名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanList) Reset() {
	*x = DoaBacaanList{}
	mi := &file_islamic_v1_surah_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanList) ProtoMessage() {}

func (x *DoaBacaanList) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanList.ProtoReflect.Descriptor instead.
func (*DoaBacaanList) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{47}
}

func (x *DoaBacaanList) GetNumber() uint32 {
	if x != nil {
		return x.Number
	}
	return 0
}

func (x *DoaBacaanList) GetBacaanId() uint32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *DoaBacaanList) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *DoaBacaanList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaBacaanListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DoaBacaanList       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanListResData) Reset() {
	*x = DoaBacaanListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanListResData) ProtoMessage() {}

func (x *DoaBacaanListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanListResData.ProtoReflect.Descriptor instead.
func (*DoaBacaanListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{48}
}

func (x *DoaBacaanListResData) GetList() []*DoaBacaanList {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaBacaanListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaBacaanListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *DoaBacaanListResData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanListRes) Reset() {
	*x = DoaBacaanListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanListRes) ProtoMessage() {}

func (x *DoaBacaanListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanListRes.ProtoReflect.Descriptor instead.
func (*DoaBacaanListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{49}
}

func (x *DoaBacaanListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaBacaanListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaBacaanListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaBacaanListRes) GetData() *DoaBacaanListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// Doa&wirid search 首页 信息列表
type DoaWiridSearchListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`   // 名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaWiridSearchListReq) Reset() {
	*x = DoaWiridSearchListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaWiridSearchListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchListReq) ProtoMessage() {}

func (x *DoaWiridSearchListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchListReq.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{50}
}

func (x *DoaWiridSearchListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaWiridSearchListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DoaWiridSearchList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BacaanId      uint32                 `protobuf:"varint,1,opt,name=bacaanId,proto3" json:"bacaanId,omitempty" dc:"bacaanId"`        // bacaanId
	BacaanName    string                 `protobuf:"bytes,2,opt,name=bacaanName,proto3" json:"bacaanName,omitempty" dc:"bacaanName"`   // bacaanName
	Pid           uint32                 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty" dc:"父id"`                       // 父id
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                       // 名称
	Types         uint32                 `protobuf:"varint,5,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid --废弃"` // 类型，1-doa，2-wirid --废弃
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaWiridSearchList) Reset() {
	*x = DoaWiridSearchList{}
	mi := &file_islamic_v1_surah_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaWiridSearchList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchList) ProtoMessage() {}

func (x *DoaWiridSearchList) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchList.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchList) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{51}
}

func (x *DoaWiridSearchList) GetBacaanId() uint32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

func (x *DoaWiridSearchList) GetBacaanName() string {
	if x != nil {
		return x.BacaanName
	}
	return ""
}

func (x *DoaWiridSearchList) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *DoaWiridSearchList) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaWiridSearchList) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaWiridSearchListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DoaWiridSearchList  `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaWiridSearchListResData) Reset() {
	*x = DoaWiridSearchListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaWiridSearchListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchListResData) ProtoMessage() {}

func (x *DoaWiridSearchListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchListResData.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{52}
}

func (x *DoaWiridSearchListResData) GetList() []*DoaWiridSearchList {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaWiridSearchListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaWiridSearchListRes struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *DoaWiridSearchListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaWiridSearchListRes) Reset() {
	*x = DoaWiridSearchListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaWiridSearchListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaWiridSearchListRes) ProtoMessage() {}

func (x *DoaWiridSearchListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaWiridSearchListRes.ProtoReflect.Descriptor instead.
func (*DoaWiridSearchListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{53}
}

func (x *DoaWiridSearchListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaWiridSearchListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaWiridSearchListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaWiridSearchListRes) GetData() *DoaWiridSearchListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// BacaanInfo 信息列表
type DoaBacaanInfoListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	BacaanId      int32                  `protobuf:"varint,2,opt,name=bacaan_id,json=bacaanId,proto3" json:"bacaan_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanInfoListReq) Reset() {
	*x = DoaBacaanInfoListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanInfoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfoListReq) ProtoMessage() {}

func (x *DoaBacaanInfoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfoListReq.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{54}
}

func (x *DoaBacaanInfoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *DoaBacaanInfoListReq) GetBacaanId() int32 {
	if x != nil {
		return x.BacaanId
	}
	return 0
}

type DoaBacaanInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"id"`                // id
	Name          string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty" dc:"name"`           // name
	Content1      string                 `protobuf:"bytes,3,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"page参数"` // page参数
	Content2      string                 `protobuf:"bytes,4,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"经文"`     // 经文
	Content3      string                 `protobuf:"bytes,5,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"解释"`     // 解释
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanInfo) Reset() {
	*x = DoaBacaanInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfo) ProtoMessage() {}

func (x *DoaBacaanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfo.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{55}
}

func (x *DoaBacaanInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DoaBacaanInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DoaBacaanInfo) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *DoaBacaanInfo) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *DoaBacaanInfo) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

type DoaBacaanInfoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DoaBacaanInfo       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanInfoListResData) Reset() {
	*x = DoaBacaanInfoListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanInfoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfoListResData) ProtoMessage() {}

func (x *DoaBacaanInfoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfoListResData.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{56}
}

func (x *DoaBacaanInfoListResData) GetList() []*DoaBacaanInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaBacaanInfoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaBacaanInfoListRes struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *DoaBacaanInfoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaBacaanInfoListRes) Reset() {
	*x = DoaBacaanInfoListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaBacaanInfoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaBacaanInfoListRes) ProtoMessage() {}

func (x *DoaBacaanInfoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaBacaanInfoListRes.ProtoReflect.Descriptor instead.
func (*DoaBacaanInfoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{57}
}

func (x *DoaBacaanInfoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaBacaanInfoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaBacaanInfoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaBacaanInfoListRes) GetData() *DoaBacaanInfoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DoaReadCollectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BaccanId      uint32                 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id"` //子id
	Types         uint32                 `protobuf:"varint,2,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`          //类型，1-doa，2-wirid
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaReadCollectReq) Reset() {
	*x = DoaReadCollectReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectReq) ProtoMessage() {}

func (x *DoaReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectReq.ProtoReflect.Descriptor instead.
func (*DoaReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{58}
}

func (x *DoaReadCollectReq) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *DoaReadCollectReq) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaReadCollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaReadCollectRes) Reset() {
	*x = DoaReadCollectRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectRes) ProtoMessage() {}

func (x *DoaReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectRes.ProtoReflect.Descriptor instead.
func (*DoaReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{59}
}

func (x *DoaReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckDoaReadCollectStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BaccanId      uint32                 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id"` //子id
	Types         uint32                 `protobuf:"varint,2,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`          //类型，1-doa，2-wirid
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDoaReadCollectStatusReq) Reset() {
	*x = CheckDoaReadCollectStatusReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDoaReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDoaReadCollectStatusReq) ProtoMessage() {}

func (x *CheckDoaReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDoaReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckDoaReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{60}
}

func (x *CheckDoaReadCollectStatusReq) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *CheckDoaReadCollectStatusReq) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type CheckDoaReadCollectStatusResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsCollect     int32                  `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDoaReadCollectStatusResData) Reset() {
	*x = CheckDoaReadCollectStatusResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDoaReadCollectStatusResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDoaReadCollectStatusResData) ProtoMessage() {}

func (x *CheckDoaReadCollectStatusResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDoaReadCollectStatusResData.ProtoReflect.Descriptor instead.
func (*CheckDoaReadCollectStatusResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{61}
}

func (x *CheckDoaReadCollectStatusResData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type CheckDoaReadCollectStatusRes struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Code          int32                             `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                     `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *CheckDoaReadCollectStatusResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDoaReadCollectStatusRes) Reset() {
	*x = CheckDoaReadCollectStatusRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDoaReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDoaReadCollectStatusRes) ProtoMessage() {}

func (x *CheckDoaReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDoaReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckDoaReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{62}
}

func (x *CheckDoaReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckDoaReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckDoaReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckDoaReadCollectStatusRes) GetData() *CheckDoaReadCollectStatusResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type DoaReadInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BaccanId      uint32                 `protobuf:"varint,1,opt,name=baccan_id,json=baccanId,proto3" json:"baccan_id,omitempty" dc:"子id- sub-category id"`       // 子id- sub-category id
	BaccanName    string                 `protobuf:"bytes,2,opt,name=baccan_name,json=baccanName,proto3" json:"baccan_name,omitempty" dc:"子name sub-category id"` // 子name sub-category id
	PName         string                 `protobuf:"bytes,3,opt,name=p_name,json=pName,proto3" json:"p_name,omitempty" dc:"父名称  category id"`                     // 父名称  category id
	Types         uint32                 `protobuf:"varint,4,opt,name=types,proto3" json:"types,omitempty" dc:"类型，1-doa，2-wirid"`                                 // 类型，1-doa，2-wirid
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaReadInfo) Reset() {
	*x = DoaReadInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadInfo) ProtoMessage() {}

func (x *DoaReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadInfo.ProtoReflect.Descriptor instead.
func (*DoaReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{63}
}

func (x *DoaReadInfo) GetBaccanId() uint32 {
	if x != nil {
		return x.BaccanId
	}
	return 0
}

func (x *DoaReadInfo) GetBaccanName() string {
	if x != nil {
		return x.BaccanName
	}
	return ""
}

func (x *DoaReadInfo) GetPName() string {
	if x != nil {
		return x.PName
	}
	return ""
}

func (x *DoaReadInfo) GetTypes() uint32 {
	if x != nil {
		return x.Types
	}
	return 0
}

type DoaReadCollectListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaReadCollectListReq) Reset() {
	*x = DoaReadCollectListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectListReq) ProtoMessage() {}

func (x *DoaReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectListReq.ProtoReflect.Descriptor instead.
func (*DoaReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{64}
}

func (x *DoaReadCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaReadCollectListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*DoaReadInfo         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaReadCollectListResData) Reset() {
	*x = DoaReadCollectListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectListResData) ProtoMessage() {}

func (x *DoaReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectListResData.ProtoReflect.Descriptor instead.
func (*DoaReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{65}
}

func (x *DoaReadCollectListResData) GetList() []*DoaReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *DoaReadCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type DoaReadCollectListRes struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *DoaReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DoaReadCollectListRes) Reset() {
	*x = DoaReadCollectListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DoaReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoaReadCollectListRes) ProtoMessage() {}

func (x *DoaReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoaReadCollectListRes.ProtoReflect.Descriptor instead.
func (*DoaReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{66}
}

func (x *DoaReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DoaReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *DoaReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *DoaReadCollectListRes) GetData() *DoaReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_surah_proto protoreflect.FileDescriptor

const file_islamic_v1_surah_proto_rawDesc = "" +
	"\n" +
	"\x16islamic/v1/surah.proto\x12\n" +
	"islamic.v1\x1a\x19pbentity/surat_ayat.proto\x1a\x1bpbentity/surat_daftar.proto\x1a\x1bpbentity/surat_tafsir.proto\x1a\x1apbentity/news_tahlil.proto\x1a\x17common/front_info.proto\x1a\x11common/base.proto\x1a\x1egoogle/protobuf/wrappers.proto\"z\n" +
	"\fSurahListReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"is_popular\x18\x03 \x01(\rR\tisPopular\x12'\n" +
	"\x04page\x18\x04 \x01(\v2\x13.common.PageRequestR\x04page\"\xf3\x01\n" +
	"\x0fSuratDaftarInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x14\n" +
	"\x05Nomor\x18\x02 \x01(\rR\x05Nomor\x12\x12\n" +
	"\x04Nama\x18\x03 \x01(\tR\x04Nama\x12\x1c\n" +
	"\tNamaLatin\x18\x04 \x01(\tR\tNamaLatin\x12\x1e\n" +
	"\n" +
	"JumlahAyat\x18\x05 \x01(\rR\n" +
	"JumlahAyat\x12 \n" +
	"\vTempatTurun\x18\x06 \x01(\tR\vTempatTurun\x12\x12\n" +
	"\x04Arti\x18\a \x01(\tR\x04Arti\x12\x1c\n" +
	"\tDeskripsi\x18\b \x01(\tR\tDeskripsi\x12\x14\n" +
	"\x05Audio\x18\t \x01(\tR\x05Audio\"m\n" +
	"\x10SurahListResData\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.islamic.v1.SuratDaftarInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8b\x01\n" +
	"\fSurahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x01(\v2\x1c.islamic.v1.SurahListResDataR\x04data\" \n" +
	"\n" +
	"JuzListReq\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\xcd\x02\n" +
	"\aJuzInfo\x12$\n" +
	"\x0estart_surah_id\x18\x01 \x01(\rR\fstartSurahId\x12(\n" +
	"\x10start_surah_name\x18\x02 \x01(\tR\x0estartSurahName\x12 \n" +
	"\fend_surah_id\x18\x03 \x01(\rR\n" +
	"endSurahId\x12$\n" +
	"\x0eend_surah_name\x18\x04 \x01(\tR\fendSurahName\x12\"\n" +
	"\rstart_ayah_id\x18\x05 \x01(\rR\vstartAyahId\x12\x1e\n" +
	"\vend_ayah_id\x18\x06 \x01(\rR\tendAyahId\x12\x10\n" +
	"\x03juz\x18\a \x01(\tR\x03juz\x12\x1d\n" +
	"\n" +
	"first_word\x18\b \x01(\tR\tfirstWord\x12\x12\n" +
	"\x04sort\x18\t \x01(\rR\x04sort\x12!\n" +
	"\ftempat_turun\x18\n" +
	" \x01(\tR\vtempatTurun\"9\n" +
	"\x0eJuzListResData\x12'\n" +
	"\x04list\x18\x01 \x03(\v2\x13.islamic.v1.JuzInfoR\x04list\"\x87\x01\n" +
	"\n" +
	"JuzListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12.\n" +
	"\x04data\x18\x04 \x01(\v2\x1a.islamic.v1.JuzListResDataR\x04data\"\x99\x01\n" +
	"\vAyahListReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x19\n" +
	"\bsurah_id\x18\x02 \x01(\rR\asurahId\x12\x15\n" +
	"\x06juz_id\x18\x03 \x01(\rR\x05juzId\x12\x1f\n" +
	"\vpage_number\x18\x04 \x01(\rR\n" +
	"pageNumber\x12'\n" +
	"\x04page\x18\x05 \x01(\v2\x13.common.PageRequestR\x04page\"\xad\x02\n" +
	"\rSuratAyatInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x16\n" +
	"\x06AyatId\x18\x02 \x01(\rR\x06AyatId\x12\x18\n" +
	"\aSurahId\x18\x03 \x01(\rR\aSurahId\x12\x14\n" +
	"\x05Nomor\x18\x04 \x01(\rR\x05Nomor\x12\x0e\n" +
	"\x02Tr\x18\x05 \x01(\tR\x02Tr\x12\x10\n" +
	"\x03Idn\x18\x06 \x01(\tR\x03Idn\x12\x0e\n" +
	"\x02Ar\x18\a \x01(\tR\x02Ar\x12\x16\n" +
	"\x06Tafsir\x18\b \x01(\tR\x06Tafsir\x12\x10\n" +
	"\x03Juz\x18\t \x01(\rR\x03Juz\x12\x12\n" +
	"\x04Page\x18\n" +
	" \x01(\rR\x04Page\x12\x1c\n" +
	"\tSurahName\x18\v \x01(\tR\tSurahName\x12\x14\n" +
	"\x05Wajiz\x18\f \x01(\tR\x05Wajiz\x12 \n" +
	"\vTempatTurun\x18\r \x01(\tR\vTempatTurun\"j\n" +
	"\x0fAyahListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.SuratAyatInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x89\x01\n" +
	"\vAyahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.islamic.v1.AyahListResDataR\x04data\"J\n" +
	"\x11AyahReadRecordReq\x12\x17\n" +
	"\aayah_id\x18\x01 \x01(\rR\x06ayahId\x12\x1c\n" +
	"\n" +
	"is_user_op\x18\x02 \x01(\rR\bisUserOp\"^\n" +
	"\x11AyahReadRecordRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"D\n" +
	"\x12AyahReadCollectReq\x12\x17\n" +
	"\aayah_id\x18\x02 \x01(\rR\x06ayahId\x12\x15\n" +
	"\x06is_add\x18\x03 \x01(\rR\x05isAdd\"_\n" +
	"\x12AyahReadCollectRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"8\n" +
	"\x1dCheckAyahReadCollectStatusReq\x12\x17\n" +
	"\aayah_id\x18\x01 \x01(\rR\x06ayahId\"B\n" +
	"!CheckAyahReadCollectStatusResData\x12\x1d\n" +
	"\n" +
	"is_collect\x18\x01 \x01(\x05R\tisCollect\"\xad\x01\n" +
	"\x1dCheckAyahReadCollectStatusRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12A\n" +
	"\x04data\x18\x04 \x01(\v2-.islamic.v1.CheckAyahReadCollectStatusResDataR\x04data\"\xa8\x02\n" +
	"\bReadInfo\x12\x19\n" +
	"\bsurah_id\x18\x01 \x01(\rR\asurahId\x12\x1d\n" +
	"\n" +
	"surah_name\x18\x02 \x01(\tR\tsurahName\x12\x17\n" +
	"\aayah_id\x18\x03 \x01(\rR\x06ayahId\x12\x15\n" +
	"\x06juz_id\x18\x04 \x01(\rR\x05juzId\x12\x12\n" +
	"\x04arti\x18\x05 \x01(\tR\x04arti\x12\x14\n" +
	"\x05ayahs\x18\x06 \x01(\rR\x05ayahs\x12\x1d\n" +
	"\n" +
	"first_word\x18\a \x01(\tR\tfirstWord\x12\x0e\n" +
	"\x02ar\x18\b \x01(\tR\x02ar\x12\x0e\n" +
	"\x02tr\x18\t \x01(\tR\x02tr\x12\x10\n" +
	"\x03idn\x18\n" +
	" \x01(\tR\x03idn\x12!\n" +
	"\fcollect_time\x18\v \x01(\x03R\vcollectTime\x12\x14\n" +
	"\x05nomor\x18\f \x01(\rR\x05nomor\"@\n" +
	"\x15AyahReadRecordListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"o\n" +
	"\x19AyahReadRecordListResData\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.islamic.v1.ReadInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9d\x01\n" +
	"\x15AyahReadRecordListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x129\n" +
	"\x04data\x18\x04 \x01(\v2%.islamic.v1.AyahReadRecordListResDataR\x04data\"A\n" +
	"\x16AyahReadCollectListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"p\n" +
	"\x1aAyahReadCollectListResData\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.islamic.v1.ReadInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9f\x01\n" +
	"\x16AyahReadCollectListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12:\n" +
	"\x04data\x18\x04 \x01(\v2&.islamic.v1.AyahReadCollectListResDataR\x04data\"8\n" +
	"\rTahlilListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"\x88\x01\n" +
	"\x0eNewsTahlilInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12\x1a\n" +
	"\bContent1\x18\x03 \x01(\tR\bContent1\x12\x1a\n" +
	"\bContent2\x18\x04 \x01(\tR\bContent2\x12\x1a\n" +
	"\bContent3\x18\x05 \x01(\tR\bContent3\"m\n" +
	"\x11TahlilListResData\x12.\n" +
	"\x04list\x18\x01 \x03(\v2\x1a.islamic.v1.NewsTahlilInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8d\x01\n" +
	"\rTahlilListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x121\n" +
	"\x04data\x18\x04 \x01(\v2\x1d.islamic.v1.TahlilListResDataR\x04data\"K\n" +
	"\fWiridListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\"I\n" +
	"\tWiridInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12\x18\n" +
	"\aBacaans\x18\x03 \x01(\rR\aBacaans\"g\n" +
	"\x10WiridListResData\x12)\n" +
	"\x04list\x18\x01 \x03(\v2\x15.islamic.v1.WiridInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8b\x01\n" +
	"\fWiridListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x01(\v2\x1c.islamic.v1.WiridListResDataR\x04data\"l\n" +
	"\x12WiridBacaanListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x19\n" +
	"\bwirid_id\x18\x02 \x01(\x05R\awiridId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"k\n" +
	"\x0fWiridBacaanList\x12\x16\n" +
	"\x06number\x18\x01 \x01(\rR\x06number\x12\x1a\n" +
	"\bbacaanId\x18\x02 \x01(\rR\bbacaanId\x12\x10\n" +
	"\x03pid\x18\x03 \x01(\rR\x03pid\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\"s\n" +
	"\x16WiridBacaanListResData\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.islamic.v1.WiridBacaanListR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x97\x01\n" +
	"\x12WiridBacaanListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x126\n" +
	"\x04data\x18\x04 \x01(\v2\".islamic.v1.WiridBacaanListResDataR\x04data\"^\n" +
	"\x16WiridBacaanInfoListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x1b\n" +
	"\tbacaan_id\x18\x02 \x01(\x05R\bbacaanId\"\x89\x01\n" +
	"\x0fWiridBacaanInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12\x1a\n" +
	"\bContent1\x18\x03 \x01(\tR\bContent1\x12\x1a\n" +
	"\bContent2\x18\x04 \x01(\tR\bContent2\x12\x1a\n" +
	"\bContent3\x18\x05 \x01(\tR\bContent3\"w\n" +
	"\x1aWiridBacaanInfoListResData\x12/\n" +
	"\x04list\x18\x01 \x03(\v2\x1b.islamic.v1.WiridBacaanInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9f\x01\n" +
	"\x16WiridBacaanInfoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12:\n" +
	"\x04data\x18\x04 \x01(\v2&.islamic.v1.WiridBacaanInfoListResDataR\x04data\"I\n" +
	"\n" +
	"DoaListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\"G\n" +
	"\aDoaInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12\x18\n" +
	"\aBacaans\x18\x03 \x01(\rR\aBacaans\"c\n" +
	"\x0eDoaListResData\x12'\n" +
	"\x04list\x18\x01 \x03(\v2\x13.islamic.v1.DoaInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x87\x01\n" +
	"\n" +
	"DoaListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12.\n" +
	"\x04data\x18\x04 \x01(\v2\x1a.islamic.v1.DoaListResDataR\x04data\"f\n" +
	"\x10DoaBacaanListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x15\n" +
	"\x06doa_id\x18\x02 \x01(\x05R\x05doaId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"i\n" +
	"\rDoaBacaanList\x12\x16\n" +
	"\x06number\x18\x01 \x01(\rR\x06number\x12\x1a\n" +
	"\bbacaanId\x18\x02 \x01(\rR\bbacaanId\x12\x10\n" +
	"\x03pid\x18\x03 \x01(\rR\x03pid\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\"o\n" +
	"\x14DoaBacaanListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.DoaBacaanListR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x93\x01\n" +
	"\x10DoaBacaanListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x124\n" +
	"\x04data\x18\x04 \x01(\v2 .islamic.v1.DoaBacaanListResDataR\x04data\"T\n" +
	"\x15DoaWiridSearchListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x8c\x01\n" +
	"\x12DoaWiridSearchList\x12\x1a\n" +
	"\bbacaanId\x18\x01 \x01(\rR\bbacaanId\x12\x1e\n" +
	"\n" +
	"bacaanName\x18\x02 \x01(\tR\n" +
	"bacaanName\x12\x10\n" +
	"\x03pid\x18\x03 \x01(\rR\x03pid\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x14\n" +
	"\x05types\x18\x05 \x01(\rR\x05types\"y\n" +
	"\x19DoaWiridSearchListResData\x122\n" +
	"\x04list\x18\x01 \x03(\v2\x1e.islamic.v1.DoaWiridSearchListR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9d\x01\n" +
	"\x15DoaWiridSearchListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x129\n" +
	"\x04data\x18\x04 \x01(\v2%.islamic.v1.DoaWiridSearchListResDataR\x04data\"\\\n" +
	"\x14DoaBacaanInfoListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\x12\x1b\n" +
	"\tbacaan_id\x18\x02 \x01(\x05R\bbacaanId\"\x87\x01\n" +
	"\rDoaBacaanInfo\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04Name\x18\x02 \x01(\tR\x04Name\x12\x1a\n" +
	"\bContent1\x18\x03 \x01(\tR\bContent1\x12\x1a\n" +
	"\bContent2\x18\x04 \x01(\tR\bContent2\x12\x1a\n" +
	"\bContent3\x18\x05 \x01(\tR\bContent3\"s\n" +
	"\x18DoaBacaanInfoListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.DoaBacaanInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9b\x01\n" +
	"\x14DoaBacaanInfoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x128\n" +
	"\x04data\x18\x04 \x01(\v2$.islamic.v1.DoaBacaanInfoListResDataR\x04data\"F\n" +
	"\x11DoaReadCollectReq\x12\x1b\n" +
	"\tbaccan_id\x18\x01 \x01(\rR\bbaccanId\x12\x14\n" +
	"\x05types\x18\x02 \x01(\rR\x05types\"^\n" +
	"\x11DoaReadCollectRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"Q\n" +
	"\x1cCheckDoaReadCollectStatusReq\x12\x1b\n" +
	"\tbaccan_id\x18\x01 \x01(\rR\bbaccanId\x12\x14\n" +
	"\x05types\x18\x02 \x01(\rR\x05types\"A\n" +
	" CheckDoaReadCollectStatusResData\x12\x1d\n" +
	"\n" +
	"is_collect\x18\x01 \x01(\x05R\tisCollect\"\xab\x01\n" +
	"\x1cCheckDoaReadCollectStatusRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12@\n" +
	"\x04data\x18\x04 \x01(\v2,.islamic.v1.CheckDoaReadCollectStatusResDataR\x04data\"x\n" +
	"\vDoaReadInfo\x12\x1b\n" +
	"\tbaccan_id\x18\x01 \x01(\rR\bbaccanId\x12\x1f\n" +
	"\vbaccan_name\x18\x02 \x01(\tR\n" +
	"baccanName\x12\x15\n" +
	"\x06p_name\x18\x03 \x01(\tR\x05pName\x12\x14\n" +
	"\x05types\x18\x04 \x01(\rR\x05types\"@\n" +
	"\x15DoaReadCollectListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"r\n" +
	"\x19DoaReadCollectListResData\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.islamic.v1.DoaReadInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9d\x01\n" +
	"\x15DoaReadCollectListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x129\n" +
	"\x04data\x18\x04 \x01(\v2%.islamic.v1.DoaReadCollectListResDataR\x04data2\xab\f\n" +
	"\fSurahService\x12?\n" +
	"\tSurahList\x12\x18.islamic.v1.SurahListReq\x1a\x18.islamic.v1.SurahListRes\x129\n" +
	"\aJuzList\x12\x16.islamic.v1.JuzListReq\x1a\x16.islamic.v1.JuzListRes\x12<\n" +
	"\bAyahList\x12\x17.islamic.v1.AyahListReq\x1a\x17.islamic.v1.AyahListRes\x12N\n" +
	"\x0eAyahReadRecord\x12\x1d.islamic.v1.AyahReadRecordReq\x1a\x1d.islamic.v1.AyahReadRecordRes\x12Z\n" +
	"\x12AyahReadRecordList\x12!.islamic.v1.AyahReadRecordListReq\x1a!.islamic.v1.AyahReadRecordListRes\x12r\n" +
	"\x1aCheckAyahReadCollectStatus\x12).islamic.v1.CheckAyahReadCollectStatusReq\x1a).islamic.v1.CheckAyahReadCollectStatusRes\x12Q\n" +
	"\x0fAyahReadCollect\x12\x1e.islamic.v1.AyahReadCollectReq\x1a\x1e.islamic.v1.AyahReadCollectRes\x12]\n" +
	"\x13AyahReadCollectList\x12\".islamic.v1.AyahReadCollectListReq\x1a\".islamic.v1.AyahReadCollectListRes\x12B\n" +
	"\n" +
	"TahlilList\x12\x19.islamic.v1.TahlilListReq\x1a\x19.islamic.v1.TahlilListRes\x12?\n" +
	"\tWiridList\x12\x18.islamic.v1.WiridListReq\x1a\x18.islamic.v1.WiridListRes\x12Q\n" +
	"\x0fWiridBacaanList\x12\x1e.islamic.v1.WiridBacaanListReq\x1a\x1e.islamic.v1.WiridBacaanListRes\x12]\n" +
	"\x13WiridBacaanInfoList\x12\".islamic.v1.WiridBacaanInfoListReq\x1a\".islamic.v1.WiridBacaanInfoListRes\x129\n" +
	"\aDoaList\x12\x16.islamic.v1.DoaListReq\x1a\x16.islamic.v1.DoaListRes\x12K\n" +
	"\rDoaBacaanList\x12\x1c.islamic.v1.DoaBacaanListReq\x1a\x1c.islamic.v1.DoaBacaanListRes\x12W\n" +
	"\x11DoaBacaanInfoList\x12 .islamic.v1.DoaBacaanInfoListReq\x1a .islamic.v1.DoaBacaanInfoListRes\x12o\n" +
	"\x19CheckDoaReadCollectStatus\x12(.islamic.v1.CheckDoaReadCollectStatusReq\x1a(.islamic.v1.CheckDoaReadCollectStatusRes\x12N\n" +
	"\x0eDoaReadCollect\x12\x1d.islamic.v1.DoaReadCollectReq\x1a\x1d.islamic.v1.DoaReadCollectRes\x12Z\n" +
	"\x12DoaReadCollectList\x12!.islamic.v1.DoaReadCollectListReq\x1a!.islamic.v1.DoaReadCollectListRes\x12Z\n" +
	"\x12DoaWiridSearchList\x12!.islamic.v1.DoaWiridSearchListReq\x1a!.islamic.v1.DoaWiridSearchListResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_surah_proto_rawDescOnce sync.Once
	file_islamic_v1_surah_proto_rawDescData []byte
)

func file_islamic_v1_surah_proto_rawDescGZIP() []byte {
	file_islamic_v1_surah_proto_rawDescOnce.Do(func() {
		file_islamic_v1_surah_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_surah_proto_rawDesc), len(file_islamic_v1_surah_proto_rawDesc)))
	})
	return file_islamic_v1_surah_proto_rawDescData
}

var file_islamic_v1_surah_proto_msgTypes = make([]protoimpl.MessageInfo, 67)
var file_islamic_v1_surah_proto_goTypes = []any{
	(*SurahListReq)(nil),                      // 0: islamic.v1.SurahListReq
	(*SuratDaftarInfo)(nil),                   // 1: islamic.v1.SuratDaftarInfo
	(*SurahListResData)(nil),                  // 2: islamic.v1.SurahListResData
	(*SurahListRes)(nil),                      // 3: islamic.v1.SurahListRes
	(*JuzListReq)(nil),                        // 4: islamic.v1.JuzListReq
	(*JuzInfo)(nil),                           // 5: islamic.v1.JuzInfo
	(*JuzListResData)(nil),                    // 6: islamic.v1.JuzListResData
	(*JuzListRes)(nil),                        // 7: islamic.v1.JuzListRes
	(*AyahListReq)(nil),                       // 8: islamic.v1.AyahListReq
	(*SuratAyatInfo)(nil),                     // 9: islamic.v1.SuratAyatInfo
	(*AyahListResData)(nil),                   // 10: islamic.v1.AyahListResData
	(*AyahListRes)(nil),                       // 11: islamic.v1.AyahListRes
	(*AyahReadRecordReq)(nil),                 // 12: islamic.v1.AyahReadRecordReq
	(*AyahReadRecordRes)(nil),                 // 13: islamic.v1.AyahReadRecordRes
	(*AyahReadCollectReq)(nil),                // 14: islamic.v1.AyahReadCollectReq
	(*AyahReadCollectRes)(nil),                // 15: islamic.v1.AyahReadCollectRes
	(*CheckAyahReadCollectStatusReq)(nil),     // 16: islamic.v1.CheckAyahReadCollectStatusReq
	(*CheckAyahReadCollectStatusResData)(nil), // 17: islamic.v1.CheckAyahReadCollectStatusResData
	(*CheckAyahReadCollectStatusRes)(nil),     // 18: islamic.v1.CheckAyahReadCollectStatusRes
	(*ReadInfo)(nil),                          // 19: islamic.v1.ReadInfo
	(*AyahReadRecordListReq)(nil),             // 20: islamic.v1.AyahReadRecordListReq
	(*AyahReadRecordListResData)(nil),         // 21: islamic.v1.AyahReadRecordListResData
	(*AyahReadRecordListRes)(nil),             // 22: islamic.v1.AyahReadRecordListRes
	(*AyahReadCollectListReq)(nil),            // 23: islamic.v1.AyahReadCollectListReq
	(*AyahReadCollectListResData)(nil),        // 24: islamic.v1.AyahReadCollectListResData
	(*AyahReadCollectListRes)(nil),            // 25: islamic.v1.AyahReadCollectListRes
	(*TahlilListReq)(nil),                     // 26: islamic.v1.TahlilListReq
	(*NewsTahlilInfo)(nil),                    // 27: islamic.v1.NewsTahlilInfo
	(*TahlilListResData)(nil),                 // 28: islamic.v1.TahlilListResData
	(*TahlilListRes)(nil),                     // 29: islamic.v1.TahlilListRes
	(*WiridListReq)(nil),                      // 30: islamic.v1.WiridListReq
	(*WiridInfo)(nil),                         // 31: islamic.v1.WiridInfo
	(*WiridListResData)(nil),                  // 32: islamic.v1.WiridListResData
	(*WiridListRes)(nil),                      // 33: islamic.v1.WiridListRes
	(*WiridBacaanListReq)(nil),                // 34: islamic.v1.WiridBacaanListReq
	(*WiridBacaanList)(nil),                   // 35: islamic.v1.WiridBacaanList
	(*WiridBacaanListResData)(nil),            // 36: islamic.v1.WiridBacaanListResData
	(*WiridBacaanListRes)(nil),                // 37: islamic.v1.WiridBacaanListRes
	(*WiridBacaanInfoListReq)(nil),            // 38: islamic.v1.WiridBacaanInfoListReq
	(*WiridBacaanInfo)(nil),                   // 39: islamic.v1.WiridBacaanInfo
	(*WiridBacaanInfoListResData)(nil),        // 40: islamic.v1.WiridBacaanInfoListResData
	(*WiridBacaanInfoListRes)(nil),            // 41: islamic.v1.WiridBacaanInfoListRes
	(*DoaListReq)(nil),                        // 42: islamic.v1.DoaListReq
	(*DoaInfo)(nil),                           // 43: islamic.v1.DoaInfo
	(*DoaListResData)(nil),                    // 44: islamic.v1.DoaListResData
	(*DoaListRes)(nil),                        // 45: islamic.v1.DoaListRes
	(*DoaBacaanListReq)(nil),                  // 46: islamic.v1.DoaBacaanListReq
	(*DoaBacaanList)(nil),                     // 47: islamic.v1.DoaBacaanList
	(*DoaBacaanListResData)(nil),              // 48: islamic.v1.DoaBacaanListResData
	(*DoaBacaanListRes)(nil),                  // 49: islamic.v1.DoaBacaanListRes
	(*DoaWiridSearchListReq)(nil),             // 50: islamic.v1.DoaWiridSearchListReq
	(*DoaWiridSearchList)(nil),                // 51: islamic.v1.DoaWiridSearchList
	(*DoaWiridSearchListResData)(nil),         // 52: islamic.v1.DoaWiridSearchListResData
	(*DoaWiridSearchListRes)(nil),             // 53: islamic.v1.DoaWiridSearchListRes
	(*DoaBacaanInfoListReq)(nil),              // 54: islamic.v1.DoaBacaanInfoListReq
	(*DoaBacaanInfo)(nil),                     // 55: islamic.v1.DoaBacaanInfo
	(*DoaBacaanInfoListResData)(nil),          // 56: islamic.v1.DoaBacaanInfoListResData
	(*DoaBacaanInfoListRes)(nil),              // 57: islamic.v1.DoaBacaanInfoListRes
	(*DoaReadCollectReq)(nil),                 // 58: islamic.v1.DoaReadCollectReq
	(*DoaReadCollectRes)(nil),                 // 59: islamic.v1.DoaReadCollectRes
	(*CheckDoaReadCollectStatusReq)(nil),      // 60: islamic.v1.CheckDoaReadCollectStatusReq
	(*CheckDoaReadCollectStatusResData)(nil),  // 61: islamic.v1.CheckDoaReadCollectStatusResData
	(*CheckDoaReadCollectStatusRes)(nil),      // 62: islamic.v1.CheckDoaReadCollectStatusRes
	(*DoaReadInfo)(nil),                       // 63: islamic.v1.DoaReadInfo
	(*DoaReadCollectListReq)(nil),             // 64: islamic.v1.DoaReadCollectListReq
	(*DoaReadCollectListResData)(nil),         // 65: islamic.v1.DoaReadCollectListResData
	(*DoaReadCollectListRes)(nil),             // 66: islamic.v1.DoaReadCollectListRes
	(*common.PageRequest)(nil),                // 67: common.PageRequest
	(*common.PageResponse)(nil),               // 68: common.PageResponse
	(*common.Error)(nil),                      // 69: common.Error
}
var file_islamic_v1_surah_proto_depIdxs = []int32{
	67, // 0: islamic.v1.SurahListReq.page:type_name -> common.PageRequest
	1,  // 1: islamic.v1.SurahListResData.list:type_name -> islamic.v1.SuratDaftarInfo
	68, // 2: islamic.v1.SurahListResData.page:type_name -> common.PageResponse
	69, // 3: islamic.v1.SurahListRes.error:type_name -> common.Error
	2,  // 4: islamic.v1.SurahListRes.data:type_name -> islamic.v1.SurahListResData
	5,  // 5: islamic.v1.JuzListResData.list:type_name -> islamic.v1.JuzInfo
	69, // 6: islamic.v1.JuzListRes.error:type_name -> common.Error
	6,  // 7: islamic.v1.JuzListRes.data:type_name -> islamic.v1.JuzListResData
	67, // 8: islamic.v1.AyahListReq.page:type_name -> common.PageRequest
	9,  // 9: islamic.v1.AyahListResData.list:type_name -> islamic.v1.SuratAyatInfo
	68, // 10: islamic.v1.AyahListResData.page:type_name -> common.PageResponse
	69, // 11: islamic.v1.AyahListRes.error:type_name -> common.Error
	10, // 12: islamic.v1.AyahListRes.data:type_name -> islamic.v1.AyahListResData
	69, // 13: islamic.v1.AyahReadRecordRes.error:type_name -> common.Error
	69, // 14: islamic.v1.AyahReadCollectRes.error:type_name -> common.Error
	69, // 15: islamic.v1.CheckAyahReadCollectStatusRes.error:type_name -> common.Error
	17, // 16: islamic.v1.CheckAyahReadCollectStatusRes.data:type_name -> islamic.v1.CheckAyahReadCollectStatusResData
	67, // 17: islamic.v1.AyahReadRecordListReq.page:type_name -> common.PageRequest
	19, // 18: islamic.v1.AyahReadRecordListResData.list:type_name -> islamic.v1.ReadInfo
	68, // 19: islamic.v1.AyahReadRecordListResData.page:type_name -> common.PageResponse
	69, // 20: islamic.v1.AyahReadRecordListRes.error:type_name -> common.Error
	21, // 21: islamic.v1.AyahReadRecordListRes.data:type_name -> islamic.v1.AyahReadRecordListResData
	67, // 22: islamic.v1.AyahReadCollectListReq.page:type_name -> common.PageRequest
	19, // 23: islamic.v1.AyahReadCollectListResData.list:type_name -> islamic.v1.ReadInfo
	68, // 24: islamic.v1.AyahReadCollectListResData.page:type_name -> common.PageResponse
	69, // 25: islamic.v1.AyahReadCollectListRes.error:type_name -> common.Error
	24, // 26: islamic.v1.AyahReadCollectListRes.data:type_name -> islamic.v1.AyahReadCollectListResData
	67, // 27: islamic.v1.TahlilListReq.page:type_name -> common.PageRequest
	27, // 28: islamic.v1.TahlilListResData.list:type_name -> islamic.v1.NewsTahlilInfo
	68, // 29: islamic.v1.TahlilListResData.page:type_name -> common.PageResponse
	69, // 30: islamic.v1.TahlilListRes.error:type_name -> common.Error
	28, // 31: islamic.v1.TahlilListRes.data:type_name -> islamic.v1.TahlilListResData
	67, // 32: islamic.v1.WiridListReq.page:type_name -> common.PageRequest
	31, // 33: islamic.v1.WiridListResData.list:type_name -> islamic.v1.WiridInfo
	68, // 34: islamic.v1.WiridListResData.page:type_name -> common.PageResponse
	69, // 35: islamic.v1.WiridListRes.error:type_name -> common.Error
	32, // 36: islamic.v1.WiridListRes.data:type_name -> islamic.v1.WiridListResData
	67, // 37: islamic.v1.WiridBacaanListReq.page:type_name -> common.PageRequest
	35, // 38: islamic.v1.WiridBacaanListResData.list:type_name -> islamic.v1.WiridBacaanList
	68, // 39: islamic.v1.WiridBacaanListResData.page:type_name -> common.PageResponse
	69, // 40: islamic.v1.WiridBacaanListRes.error:type_name -> common.Error
	36, // 41: islamic.v1.WiridBacaanListRes.data:type_name -> islamic.v1.WiridBacaanListResData
	67, // 42: islamic.v1.WiridBacaanInfoListReq.page:type_name -> common.PageRequest
	39, // 43: islamic.v1.WiridBacaanInfoListResData.list:type_name -> islamic.v1.WiridBacaanInfo
	68, // 44: islamic.v1.WiridBacaanInfoListResData.page:type_name -> common.PageResponse
	69, // 45: islamic.v1.WiridBacaanInfoListRes.error:type_name -> common.Error
	40, // 46: islamic.v1.WiridBacaanInfoListRes.data:type_name -> islamic.v1.WiridBacaanInfoListResData
	67, // 47: islamic.v1.DoaListReq.page:type_name -> common.PageRequest
	43, // 48: islamic.v1.DoaListResData.list:type_name -> islamic.v1.DoaInfo
	68, // 49: islamic.v1.DoaListResData.page:type_name -> common.PageResponse
	69, // 50: islamic.v1.DoaListRes.error:type_name -> common.Error
	44, // 51: islamic.v1.DoaListRes.data:type_name -> islamic.v1.DoaListResData
	67, // 52: islamic.v1.DoaBacaanListReq.page:type_name -> common.PageRequest
	47, // 53: islamic.v1.DoaBacaanListResData.list:type_name -> islamic.v1.DoaBacaanList
	68, // 54: islamic.v1.DoaBacaanListResData.page:type_name -> common.PageResponse
	69, // 55: islamic.v1.DoaBacaanListRes.error:type_name -> common.Error
	48, // 56: islamic.v1.DoaBacaanListRes.data:type_name -> islamic.v1.DoaBacaanListResData
	67, // 57: islamic.v1.DoaWiridSearchListReq.page:type_name -> common.PageRequest
	51, // 58: islamic.v1.DoaWiridSearchListResData.list:type_name -> islamic.v1.DoaWiridSearchList
	68, // 59: islamic.v1.DoaWiridSearchListResData.page:type_name -> common.PageResponse
	69, // 60: islamic.v1.DoaWiridSearchListRes.error:type_name -> common.Error
	52, // 61: islamic.v1.DoaWiridSearchListRes.data:type_name -> islamic.v1.DoaWiridSearchListResData
	67, // 62: islamic.v1.DoaBacaanInfoListReq.page:type_name -> common.PageRequest
	55, // 63: islamic.v1.DoaBacaanInfoListResData.list:type_name -> islamic.v1.DoaBacaanInfo
	68, // 64: islamic.v1.DoaBacaanInfoListResData.page:type_name -> common.PageResponse
	69, // 65: islamic.v1.DoaBacaanInfoListRes.error:type_name -> common.Error
	56, // 66: islamic.v1.DoaBacaanInfoListRes.data:type_name -> islamic.v1.DoaBacaanInfoListResData
	69, // 67: islamic.v1.DoaReadCollectRes.error:type_name -> common.Error
	69, // 68: islamic.v1.CheckDoaReadCollectStatusRes.error:type_name -> common.Error
	61, // 69: islamic.v1.CheckDoaReadCollectStatusRes.data:type_name -> islamic.v1.CheckDoaReadCollectStatusResData
	67, // 70: islamic.v1.DoaReadCollectListReq.page:type_name -> common.PageRequest
	63, // 71: islamic.v1.DoaReadCollectListResData.list:type_name -> islamic.v1.DoaReadInfo
	68, // 72: islamic.v1.DoaReadCollectListResData.page:type_name -> common.PageResponse
	69, // 73: islamic.v1.DoaReadCollectListRes.error:type_name -> common.Error
	65, // 74: islamic.v1.DoaReadCollectListRes.data:type_name -> islamic.v1.DoaReadCollectListResData
	0,  // 75: islamic.v1.SurahService.SurahList:input_type -> islamic.v1.SurahListReq
	4,  // 76: islamic.v1.SurahService.JuzList:input_type -> islamic.v1.JuzListReq
	8,  // 77: islamic.v1.SurahService.AyahList:input_type -> islamic.v1.AyahListReq
	12, // 78: islamic.v1.SurahService.AyahReadRecord:input_type -> islamic.v1.AyahReadRecordReq
	20, // 79: islamic.v1.SurahService.AyahReadRecordList:input_type -> islamic.v1.AyahReadRecordListReq
	16, // 80: islamic.v1.SurahService.CheckAyahReadCollectStatus:input_type -> islamic.v1.CheckAyahReadCollectStatusReq
	14, // 81: islamic.v1.SurahService.AyahReadCollect:input_type -> islamic.v1.AyahReadCollectReq
	23, // 82: islamic.v1.SurahService.AyahReadCollectList:input_type -> islamic.v1.AyahReadCollectListReq
	26, // 83: islamic.v1.SurahService.TahlilList:input_type -> islamic.v1.TahlilListReq
	30, // 84: islamic.v1.SurahService.WiridList:input_type -> islamic.v1.WiridListReq
	34, // 85: islamic.v1.SurahService.WiridBacaanList:input_type -> islamic.v1.WiridBacaanListReq
	38, // 86: islamic.v1.SurahService.WiridBacaanInfoList:input_type -> islamic.v1.WiridBacaanInfoListReq
	42, // 87: islamic.v1.SurahService.DoaList:input_type -> islamic.v1.DoaListReq
	46, // 88: islamic.v1.SurahService.DoaBacaanList:input_type -> islamic.v1.DoaBacaanListReq
	54, // 89: islamic.v1.SurahService.DoaBacaanInfoList:input_type -> islamic.v1.DoaBacaanInfoListReq
	60, // 90: islamic.v1.SurahService.CheckDoaReadCollectStatus:input_type -> islamic.v1.CheckDoaReadCollectStatusReq
	58, // 91: islamic.v1.SurahService.DoaReadCollect:input_type -> islamic.v1.DoaReadCollectReq
	64, // 92: islamic.v1.SurahService.DoaReadCollectList:input_type -> islamic.v1.DoaReadCollectListReq
	50, // 93: islamic.v1.SurahService.DoaWiridSearchList:input_type -> islamic.v1.DoaWiridSearchListReq
	3,  // 94: islamic.v1.SurahService.SurahList:output_type -> islamic.v1.SurahListRes
	7,  // 95: islamic.v1.SurahService.JuzList:output_type -> islamic.v1.JuzListRes
	11, // 96: islamic.v1.SurahService.AyahList:output_type -> islamic.v1.AyahListRes
	13, // 97: islamic.v1.SurahService.AyahReadRecord:output_type -> islamic.v1.AyahReadRecordRes
	22, // 98: islamic.v1.SurahService.AyahReadRecordList:output_type -> islamic.v1.AyahReadRecordListRes
	18, // 99: islamic.v1.SurahService.CheckAyahReadCollectStatus:output_type -> islamic.v1.CheckAyahReadCollectStatusRes
	15, // 100: islamic.v1.SurahService.AyahReadCollect:output_type -> islamic.v1.AyahReadCollectRes
	25, // 101: islamic.v1.SurahService.AyahReadCollectList:output_type -> islamic.v1.AyahReadCollectListRes
	29, // 102: islamic.v1.SurahService.TahlilList:output_type -> islamic.v1.TahlilListRes
	33, // 103: islamic.v1.SurahService.WiridList:output_type -> islamic.v1.WiridListRes
	37, // 104: islamic.v1.SurahService.WiridBacaanList:output_type -> islamic.v1.WiridBacaanListRes
	41, // 105: islamic.v1.SurahService.WiridBacaanInfoList:output_type -> islamic.v1.WiridBacaanInfoListRes
	45, // 106: islamic.v1.SurahService.DoaList:output_type -> islamic.v1.DoaListRes
	49, // 107: islamic.v1.SurahService.DoaBacaanList:output_type -> islamic.v1.DoaBacaanListRes
	57, // 108: islamic.v1.SurahService.DoaBacaanInfoList:output_type -> islamic.v1.DoaBacaanInfoListRes
	62, // 109: islamic.v1.SurahService.CheckDoaReadCollectStatus:output_type -> islamic.v1.CheckDoaReadCollectStatusRes
	59, // 110: islamic.v1.SurahService.DoaReadCollect:output_type -> islamic.v1.DoaReadCollectRes
	66, // 111: islamic.v1.SurahService.DoaReadCollectList:output_type -> islamic.v1.DoaReadCollectListRes
	53, // 112: islamic.v1.SurahService.DoaWiridSearchList:output_type -> islamic.v1.DoaWiridSearchListRes
	94, // [94:113] is the sub-list for method output_type
	75, // [75:94] is the sub-list for method input_type
	75, // [75:75] is the sub-list for extension type_name
	75, // [75:75] is the sub-list for extension extendee
	0,  // [0:75] is the sub-list for field type_name
}

func init() { file_islamic_v1_surah_proto_init() }
func file_islamic_v1_surah_proto_init() {
	if File_islamic_v1_surah_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_surah_proto_rawDesc), len(file_islamic_v1_surah_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   67,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_surah_proto_goTypes,
		DependencyIndexes: file_islamic_v1_surah_proto_depIdxs,
		MessageInfos:      file_islamic_v1_surah_proto_msgTypes,
	}.Build()
	File_islamic_v1_surah_proto = out.File
	file_islamic_v1_surah_proto_goTypes = nil
	file_islamic_v1_surah_proto_depIdxs = nil
}
