// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.2
// source: islamic/v1/check_update.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CheckUpdateService_CheckUpdate_FullMethodName = "/islamic.v1.CheckUpdateService/CheckUpdate"
)

// CheckUpdateServiceClient is the client API for CheckUpdateService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CheckUpdateServiceClient interface {
	// app 检查更新
	CheckUpdate(ctx context.Context, in *CheckUpdateReq, opts ...grpc.CallOption) (*CheckUpdateRes, error)
}

type checkUpdateServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCheckUpdateServiceClient(cc grpc.ClientConnInterface) CheckUpdateServiceClient {
	return &checkUpdateServiceClient{cc}
}

func (c *checkUpdateServiceClient) CheckUpdate(ctx context.Context, in *CheckUpdateReq, opts ...grpc.CallOption) (*CheckUpdateRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUpdateRes)
	err := c.cc.Invoke(ctx, CheckUpdateService_CheckUpdate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CheckUpdateServiceServer is the server API for CheckUpdateService service.
// All implementations must embed UnimplementedCheckUpdateServiceServer
// for forward compatibility.
type CheckUpdateServiceServer interface {
	// app 检查更新
	CheckUpdate(context.Context, *CheckUpdateReq) (*CheckUpdateRes, error)
	mustEmbedUnimplementedCheckUpdateServiceServer()
}

// UnimplementedCheckUpdateServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCheckUpdateServiceServer struct{}

func (UnimplementedCheckUpdateServiceServer) CheckUpdate(context.Context, *CheckUpdateReq) (*CheckUpdateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUpdate not implemented")
}
func (UnimplementedCheckUpdateServiceServer) mustEmbedUnimplementedCheckUpdateServiceServer() {}
func (UnimplementedCheckUpdateServiceServer) testEmbeddedByValue()                            {}

// UnsafeCheckUpdateServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CheckUpdateServiceServer will
// result in compilation errors.
type UnsafeCheckUpdateServiceServer interface {
	mustEmbedUnimplementedCheckUpdateServiceServer()
}

func RegisterCheckUpdateServiceServer(s grpc.ServiceRegistrar, srv CheckUpdateServiceServer) {
	// If the following call pancis, it indicates UnimplementedCheckUpdateServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CheckUpdateService_ServiceDesc, srv)
}

func _CheckUpdateService_CheckUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckUpdateServiceServer).CheckUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CheckUpdateService_CheckUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckUpdateServiceServer).CheckUpdate(ctx, req.(*CheckUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CheckUpdateService_ServiceDesc is the grpc.ServiceDesc for CheckUpdateService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CheckUpdateService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.CheckUpdateService",
	HandlerType: (*CheckUpdateServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckUpdate",
			Handler:    _CheckUpdateService_CheckUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/check_update.proto",
}
