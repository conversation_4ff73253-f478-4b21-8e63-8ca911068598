// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.27.2
// source: islamic/v1/feedback.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FeedbackService_FeedbackAdd_FullMethodName = "/islamic.v1.FeedbackService/FeedbackAdd"
)

// FeedbackServiceClient is the client API for FeedbackService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type FeedbackServiceClient interface {
	FeedbackAdd(ctx context.Context, in *FeedbackAddReq, opts ...grpc.CallOption) (*FeedbackAddRes, error)
}

type feedbackServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFeedbackServiceClient(cc grpc.ClientConnInterface) FeedbackServiceClient {
	return &feedbackServiceClient{cc}
}

func (c *feedbackServiceClient) FeedbackAdd(ctx context.Context, in *FeedbackAddReq, opts ...grpc.CallOption) (*FeedbackAddRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FeedbackAddRes)
	err := c.cc.Invoke(ctx, FeedbackService_FeedbackAdd_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FeedbackServiceServer is the server API for FeedbackService service.
// All implementations must embed UnimplementedFeedbackServiceServer
// for forward compatibility.
type FeedbackServiceServer interface {
	FeedbackAdd(context.Context, *FeedbackAddReq) (*FeedbackAddRes, error)
	mustEmbedUnimplementedFeedbackServiceServer()
}

// UnimplementedFeedbackServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFeedbackServiceServer struct{}

func (UnimplementedFeedbackServiceServer) FeedbackAdd(context.Context, *FeedbackAddReq) (*FeedbackAddRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FeedbackAdd not implemented")
}
func (UnimplementedFeedbackServiceServer) mustEmbedUnimplementedFeedbackServiceServer() {}
func (UnimplementedFeedbackServiceServer) testEmbeddedByValue()                         {}

// UnsafeFeedbackServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FeedbackServiceServer will
// result in compilation errors.
type UnsafeFeedbackServiceServer interface {
	mustEmbedUnimplementedFeedbackServiceServer()
}

func RegisterFeedbackServiceServer(s grpc.ServiceRegistrar, srv FeedbackServiceServer) {
	// If the following call pancis, it indicates UnimplementedFeedbackServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FeedbackService_ServiceDesc, srv)
}

func _FeedbackService_FeedbackAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FeedbackAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FeedbackServiceServer).FeedbackAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FeedbackService_FeedbackAdd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FeedbackServiceServer).FeedbackAdd(ctx, req.(*FeedbackAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

// FeedbackService_ServiceDesc is the grpc.ServiceDesc for FeedbackService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FeedbackService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.FeedbackService",
	HandlerType: (*FeedbackServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FeedbackAdd",
			Handler:    _FeedbackService_FeedbackAdd_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/feedback.proto",
}
