// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/umrah_landmark_languages.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UmrahLandmarkLanguages struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                                   // 主键ID
	LandmarkId       uint64                 `protobuf:"varint,2,opt,name=LandmarkId,proto3" json:"LandmarkId,omitempty" dc:"地标ID，关联umrah_landmark.id"`               // 地标ID，关联umrah_landmark.id
	LanguageId       uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID：0-中文，1-英文，2-印尼语"`                   // 语言ID：0-中文，1-英文，2-印尼语
	LandmarkName     string                 `protobuf:"bytes,4,opt,name=LandmarkName,proto3" json:"LandmarkName,omitempty" dc:"地标名称"`                                // 地标名称
	Country          string                 `protobuf:"bytes,5,opt,name=Country,proto3" json:"Country,omitempty" dc:"国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)"` // 国家/地区(后续使用google map api，应该是能获取到的，先使用手动输入的值吧)
	Address          string                 `protobuf:"bytes,6,opt,name=Address,proto3" json:"Address,omitempty" dc:"详细地址"`                                          // 详细地址
	ShortDescription string                 `protobuf:"bytes,7,opt,name=ShortDescription,proto3" json:"ShortDescription,omitempty" dc:"简介"`                          // 简介
	InformationText  string                 `protobuf:"bytes,8,opt,name=InformationText,proto3" json:"InformationText,omitempty" dc:"详细介绍"`                          // 详细介绍
	CreateTime       uint64                 `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`                            // 创建时间（毫秒时间戳）
	UpdateTime       uint64                 `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`                           // 更新时间（毫秒时间戳）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UmrahLandmarkLanguages) Reset() {
	*x = UmrahLandmarkLanguages{}
	mi := &file_pbentity_umrah_landmark_languages_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmarkLanguages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmarkLanguages) ProtoMessage() {}

func (x *UmrahLandmarkLanguages) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_umrah_landmark_languages_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmarkLanguages.ProtoReflect.Descriptor instead.
func (*UmrahLandmarkLanguages) Descriptor() ([]byte, []int) {
	return file_pbentity_umrah_landmark_languages_proto_rawDescGZIP(), []int{0}
}

func (x *UmrahLandmarkLanguages) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahLandmarkLanguages) GetLandmarkId() uint64 {
	if x != nil {
		return x.LandmarkId
	}
	return 0
}

func (x *UmrahLandmarkLanguages) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *UmrahLandmarkLanguages) GetLandmarkName() string {
	if x != nil {
		return x.LandmarkName
	}
	return ""
}

func (x *UmrahLandmarkLanguages) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *UmrahLandmarkLanguages) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *UmrahLandmarkLanguages) GetShortDescription() string {
	if x != nil {
		return x.ShortDescription
	}
	return ""
}

func (x *UmrahLandmarkLanguages) GetInformationText() string {
	if x != nil {
		return x.InformationText
	}
	return ""
}

func (x *UmrahLandmarkLanguages) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UmrahLandmarkLanguages) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_umrah_landmark_languages_proto protoreflect.FileDescriptor

const file_pbentity_umrah_landmark_languages_proto_rawDesc = "" +
	"\n" +
	"'pbentity/umrah_landmark_languages.proto\x12\bpbentity\"\xd6\x02\n" +
	"\x16UmrahLandmarkLanguages\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x1e\n" +
	"\n" +
	"LandmarkId\x18\x02 \x01(\x04R\n" +
	"LandmarkId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\"\n" +
	"\fLandmarkName\x18\x04 \x01(\tR\fLandmarkName\x12\x18\n" +
	"\aCountry\x18\x05 \x01(\tR\aCountry\x12\x18\n" +
	"\aAddress\x18\x06 \x01(\tR\aAddress\x12*\n" +
	"\x10ShortDescription\x18\a \x01(\tR\x10ShortDescription\x12(\n" +
	"\x0fInformationText\x18\b \x01(\tR\x0fInformationText\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\t \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\n" +
	" \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_umrah_landmark_languages_proto_rawDescOnce sync.Once
	file_pbentity_umrah_landmark_languages_proto_rawDescData []byte
)

func file_pbentity_umrah_landmark_languages_proto_rawDescGZIP() []byte {
	file_pbentity_umrah_landmark_languages_proto_rawDescOnce.Do(func() {
		file_pbentity_umrah_landmark_languages_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_umrah_landmark_languages_proto_rawDesc), len(file_pbentity_umrah_landmark_languages_proto_rawDesc)))
	})
	return file_pbentity_umrah_landmark_languages_proto_rawDescData
}

var file_pbentity_umrah_landmark_languages_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_umrah_landmark_languages_proto_goTypes = []any{
	(*UmrahLandmarkLanguages)(nil), // 0: pbentity.UmrahLandmarkLanguages
}
var file_pbentity_umrah_landmark_languages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_umrah_landmark_languages_proto_init() }
func file_pbentity_umrah_landmark_languages_proto_init() {
	if File_pbentity_umrah_landmark_languages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_umrah_landmark_languages_proto_rawDesc), len(file_pbentity_umrah_landmark_languages_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_umrah_landmark_languages_proto_goTypes,
		DependencyIndexes: file_pbentity_umrah_landmark_languages_proto_depIdxs,
		MessageInfos:      file_pbentity_umrah_landmark_languages_proto_msgTypes,
	}.Build()
	File_pbentity_umrah_landmark_languages_proto = out.File
	file_pbentity_umrah_landmark_languages_proto_goTypes = nil
	file_pbentity_umrah_landmark_languages_proto_depIdxs = nil
}
