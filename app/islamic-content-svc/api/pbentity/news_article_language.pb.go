// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/news_article_language.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsArticleLanguage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                           //
	ArticleId     uint32                 `protobuf:"varint,2,opt,name=ArticleId,proto3" json:"ArticleId,omitempty" dc:"文章id"`                   // 文章id
	LanguageId    uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言id,0-中文，1-英文，2-印尼语"` // 语言id,0-中文，1-英文，2-印尼语
	Name          string                 `protobuf:"bytes,4,opt,name=Name,proto3" json:"Name,omitempty" dc:"名称"`                                // 名称
	Content       string                 `protobuf:"bytes,5,opt,name=Content,proto3" json:"Content,omitempty" dc:"正文"`                          // 正文
	CreateTime    int64                  `protobuf:"varint,6,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`                 // 创建时间
	UpdateTime    int64                  `protobuf:"varint,7,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"修改时间"`                 // 修改时间
	DeleteTime    int64                  `protobuf:"varint,8,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`                 // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsArticleLanguage) Reset() {
	*x = NewsArticleLanguage{}
	mi := &file_pbentity_news_article_language_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsArticleLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsArticleLanguage) ProtoMessage() {}

func (x *NewsArticleLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_article_language_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsArticleLanguage.ProtoReflect.Descriptor instead.
func (*NewsArticleLanguage) Descriptor() ([]byte, []int) {
	return file_pbentity_news_article_language_proto_rawDescGZIP(), []int{0}
}

func (x *NewsArticleLanguage) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsArticleLanguage) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *NewsArticleLanguage) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *NewsArticleLanguage) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NewsArticleLanguage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *NewsArticleLanguage) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsArticleLanguage) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsArticleLanguage) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_news_article_language_proto protoreflect.FileDescriptor

const file_pbentity_news_article_language_proto_rawDesc = "" +
	"\n" +
	"$pbentity/news_article_language.proto\x12\bpbentity\"\xf1\x01\n" +
	"\x13NewsArticleLanguage\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1c\n" +
	"\tArticleId\x18\x02 \x01(\rR\tArticleId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\x12\n" +
	"\x04Name\x18\x04 \x01(\tR\x04Name\x12\x18\n" +
	"\aContent\x18\x05 \x01(\tR\aContent\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x06 \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\a \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\b \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_article_language_proto_rawDescOnce sync.Once
	file_pbentity_news_article_language_proto_rawDescData []byte
)

func file_pbentity_news_article_language_proto_rawDescGZIP() []byte {
	file_pbentity_news_article_language_proto_rawDescOnce.Do(func() {
		file_pbentity_news_article_language_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_article_language_proto_rawDesc), len(file_pbentity_news_article_language_proto_rawDesc)))
	})
	return file_pbentity_news_article_language_proto_rawDescData
}

var file_pbentity_news_article_language_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_article_language_proto_goTypes = []any{
	(*NewsArticleLanguage)(nil), // 0: pbentity.NewsArticleLanguage
}
var file_pbentity_news_article_language_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_article_language_proto_init() }
func file_pbentity_news_article_language_proto_init() {
	if File_pbentity_news_article_language_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_article_language_proto_rawDesc), len(file_pbentity_news_article_language_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_article_language_proto_goTypes,
		DependencyIndexes: file_pbentity_news_article_language_proto_depIdxs,
		MessageInfos:      file_pbentity_news_article_language_proto_msgTypes,
	}.Build()
	File_pbentity_news_article_language_proto = out.File
	file_pbentity_news_article_language_proto_goTypes = nil
	file_pbentity_news_article_language_proto_depIdxs = nil
}
