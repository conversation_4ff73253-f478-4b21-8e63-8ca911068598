// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/video_play_history.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoPlayHistory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                             // 主键ID
	UserId        uint64                 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户ID"`                     // 用户ID
	VideoId       uint32                 `protobuf:"varint,3,opt,name=VideoId,proto3" json:"VideoId,omitempty" dc:"视频ID"`                   // 视频ID
	PlayPosition  uint32                 `protobuf:"varint,4,opt,name=PlayPosition,proto3" json:"PlayPosition,omitempty" dc:"播放位置(秒)"`      // 播放位置(秒)
	PlayDuration  uint32                 `protobuf:"varint,5,opt,name=PlayDuration,proto3" json:"PlayDuration,omitempty" dc:"本次播放时长(秒)"`    // 本次播放时长(秒)
	IsCompleted   uint32                 `protobuf:"varint,6,opt,name=IsCompleted,proto3" json:"IsCompleted,omitempty" dc:"是否播放完成，0-否，1-是"` // 是否播放完成，0-否，1-是
	CreateTime    uint64                 `protobuf:"varint,7,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"播放时间(毫秒时间戳)"`      // 播放时间(毫秒时间戳)
	UpdateTime    uint64                 `protobuf:"varint,8,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`      // 更新时间(毫秒时间戳)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistory) Reset() {
	*x = VideoPlayHistory{}
	mi := &file_pbentity_video_play_history_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistory) ProtoMessage() {}

func (x *VideoPlayHistory) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_video_play_history_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistory.ProtoReflect.Descriptor instead.
func (*VideoPlayHistory) Descriptor() ([]byte, []int) {
	return file_pbentity_video_play_history_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlayHistory) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlayHistory) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *VideoPlayHistory) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoPlayHistory) GetPlayPosition() uint32 {
	if x != nil {
		return x.PlayPosition
	}
	return 0
}

func (x *VideoPlayHistory) GetPlayDuration() uint32 {
	if x != nil {
		return x.PlayDuration
	}
	return 0
}

func (x *VideoPlayHistory) GetIsCompleted() uint32 {
	if x != nil {
		return x.IsCompleted
	}
	return 0
}

func (x *VideoPlayHistory) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VideoPlayHistory) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_video_play_history_proto protoreflect.FileDescriptor

const file_pbentity_video_play_history_proto_rawDesc = "" +
	"\n" +
	"!pbentity/video_play_history.proto\x12\bpbentity\"\xfe\x01\n" +
	"\x10VideoPlayHistory\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x16\n" +
	"\x06UserId\x18\x02 \x01(\x04R\x06UserId\x12\x18\n" +
	"\aVideoId\x18\x03 \x01(\rR\aVideoId\x12\"\n" +
	"\fPlayPosition\x18\x04 \x01(\rR\fPlayPosition\x12\"\n" +
	"\fPlayDuration\x18\x05 \x01(\rR\fPlayDuration\x12 \n" +
	"\vIsCompleted\x18\x06 \x01(\rR\vIsCompleted\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\a \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\b \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_video_play_history_proto_rawDescOnce sync.Once
	file_pbentity_video_play_history_proto_rawDescData []byte
)

func file_pbentity_video_play_history_proto_rawDescGZIP() []byte {
	file_pbentity_video_play_history_proto_rawDescOnce.Do(func() {
		file_pbentity_video_play_history_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_video_play_history_proto_rawDesc), len(file_pbentity_video_play_history_proto_rawDesc)))
	})
	return file_pbentity_video_play_history_proto_rawDescData
}

var file_pbentity_video_play_history_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_video_play_history_proto_goTypes = []any{
	(*VideoPlayHistory)(nil), // 0: pbentity.VideoPlayHistory
}
var file_pbentity_video_play_history_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_video_play_history_proto_init() }
func file_pbentity_video_play_history_proto_init() {
	if File_pbentity_video_play_history_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_video_play_history_proto_rawDesc), len(file_pbentity_video_play_history_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_video_play_history_proto_goTypes,
		DependencyIndexes: file_pbentity_video_play_history_proto_depIdxs,
		MessageInfos:      file_pbentity_video_play_history_proto_msgTypes,
	}.Build()
	File_pbentity_video_play_history_proto = out.File
	file_pbentity_video_play_history_proto_goTypes = nil
	file_pbentity_video_play_history_proto_depIdxs = nil
}
