// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/news_topic_article.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsTopicArticle struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                              //
	TopicId       uint32                 `protobuf:"varint,2,opt,name=TopicId,proto3" json:"TopicId,omitempty" dc:"topic id"`      // topic id
	ArticleId     uint32                 `protobuf:"varint,3,opt,name=ArticleId,proto3" json:"ArticleId,omitempty" dc:"文章id"`      // 文章id
	ArticleName   string                 `protobuf:"bytes,4,opt,name=ArticleName,proto3" json:"ArticleName,omitempty" dc:"文章name"` // 文章name
	CreateTime    int64                  `protobuf:"varint,5,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`    // 创建时间
	UpdateTime    int64                  `protobuf:"varint,6,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"修改时间"`    // 修改时间
	DeleteTime    int64                  `protobuf:"varint,7,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`    // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsTopicArticle) Reset() {
	*x = NewsTopicArticle{}
	mi := &file_pbentity_news_topic_article_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsTopicArticle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicArticle) ProtoMessage() {}

func (x *NewsTopicArticle) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_topic_article_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicArticle.ProtoReflect.Descriptor instead.
func (*NewsTopicArticle) Descriptor() ([]byte, []int) {
	return file_pbentity_news_topic_article_proto_rawDescGZIP(), []int{0}
}

func (x *NewsTopicArticle) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsTopicArticle) GetTopicId() uint32 {
	if x != nil {
		return x.TopicId
	}
	return 0
}

func (x *NewsTopicArticle) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *NewsTopicArticle) GetArticleName() string {
	if x != nil {
		return x.ArticleName
	}
	return ""
}

func (x *NewsTopicArticle) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsTopicArticle) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsTopicArticle) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_news_topic_article_proto protoreflect.FileDescriptor

const file_pbentity_news_topic_article_proto_rawDesc = "" +
	"\n" +
	"!pbentity/news_topic_article.proto\x12\bpbentity\"\xdc\x01\n" +
	"\x10NewsTopicArticle\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x18\n" +
	"\aTopicId\x18\x02 \x01(\rR\aTopicId\x12\x1c\n" +
	"\tArticleId\x18\x03 \x01(\rR\tArticleId\x12 \n" +
	"\vArticleName\x18\x04 \x01(\tR\vArticleName\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x05 \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x06 \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\a \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_topic_article_proto_rawDescOnce sync.Once
	file_pbentity_news_topic_article_proto_rawDescData []byte
)

func file_pbentity_news_topic_article_proto_rawDescGZIP() []byte {
	file_pbentity_news_topic_article_proto_rawDescOnce.Do(func() {
		file_pbentity_news_topic_article_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_topic_article_proto_rawDesc), len(file_pbentity_news_topic_article_proto_rawDesc)))
	})
	return file_pbentity_news_topic_article_proto_rawDescData
}

var file_pbentity_news_topic_article_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_topic_article_proto_goTypes = []any{
	(*NewsTopicArticle)(nil), // 0: pbentity.NewsTopicArticle
}
var file_pbentity_news_topic_article_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_topic_article_proto_init() }
func file_pbentity_news_topic_article_proto_init() {
	if File_pbentity_news_topic_article_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_topic_article_proto_rawDesc), len(file_pbentity_news_topic_article_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_topic_article_proto_goTypes,
		DependencyIndexes: file_pbentity_news_topic_article_proto_depIdxs,
		MessageInfos:      file_pbentity_news_topic_article_proto_msgTypes,
	}.Build()
	File_pbentity_news_topic_article_proto = out.File
	file_pbentity_news_topic_article_proto_goTypes = nil
	file_pbentity_news_topic_article_proto_depIdxs = nil
}
