// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/wisdom_language.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WisdomLanguage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                         //
	WisdomId      uint32                 `protobuf:"varint,2,opt,name=WisdomId,proto3" json:"WisdomId,omitempty"`             //
	LanguageId    int32                  `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言"` // 语言
	Title         string                 `protobuf:"bytes,4,opt,name=Title,proto3" json:"Title,omitempty" dc:"名言标题"`          // 名言标题
	ImageUrl      string                 `protobuf:"bytes,5,opt,name=ImageUrl,proto3" json:"ImageUrl,omitempty" dc:"名言图片url"` // 名言图片url
	Desc          string                 `protobuf:"bytes,6,opt,name=Desc,proto3" json:"Desc,omitempty" dc:"详情"`              // 详情
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WisdomLanguage) Reset() {
	*x = WisdomLanguage{}
	mi := &file_pbentity_wisdom_language_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WisdomLanguage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WisdomLanguage) ProtoMessage() {}

func (x *WisdomLanguage) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_wisdom_language_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WisdomLanguage.ProtoReflect.Descriptor instead.
func (*WisdomLanguage) Descriptor() ([]byte, []int) {
	return file_pbentity_wisdom_language_proto_rawDescGZIP(), []int{0}
}

func (x *WisdomLanguage) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WisdomLanguage) GetWisdomId() uint32 {
	if x != nil {
		return x.WisdomId
	}
	return 0
}

func (x *WisdomLanguage) GetLanguageId() int32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *WisdomLanguage) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WisdomLanguage) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *WisdomLanguage) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

var File_pbentity_wisdom_language_proto protoreflect.FileDescriptor

const file_pbentity_wisdom_language_proto_rawDesc = "" +
	"\n" +
	"\x1epbentity/wisdom_language.proto\x12\bpbentity\"\xa2\x01\n" +
	"\x0eWisdomLanguage\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1a\n" +
	"\bWisdomId\x18\x02 \x01(\rR\bWisdomId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\x05R\n" +
	"LanguageId\x12\x14\n" +
	"\x05Title\x18\x04 \x01(\tR\x05Title\x12\x1a\n" +
	"\bImageUrl\x18\x05 \x01(\tR\bImageUrl\x12\x12\n" +
	"\x04Desc\x18\x06 \x01(\tR\x04DescB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_wisdom_language_proto_rawDescOnce sync.Once
	file_pbentity_wisdom_language_proto_rawDescData []byte
)

func file_pbentity_wisdom_language_proto_rawDescGZIP() []byte {
	file_pbentity_wisdom_language_proto_rawDescOnce.Do(func() {
		file_pbentity_wisdom_language_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_wisdom_language_proto_rawDesc), len(file_pbentity_wisdom_language_proto_rawDesc)))
	})
	return file_pbentity_wisdom_language_proto_rawDescData
}

var file_pbentity_wisdom_language_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_wisdom_language_proto_goTypes = []any{
	(*WisdomLanguage)(nil), // 0: pbentity.WisdomLanguage
}
var file_pbentity_wisdom_language_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_wisdom_language_proto_init() }
func file_pbentity_wisdom_language_proto_init() {
	if File_pbentity_wisdom_language_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_wisdom_language_proto_rawDesc), len(file_pbentity_wisdom_language_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_wisdom_language_proto_goTypes,
		DependencyIndexes: file_pbentity_wisdom_language_proto_depIdxs,
		MessageInfos:      file_pbentity_wisdom_language_proto_msgTypes,
	}.Build()
	File_pbentity_wisdom_language_proto = out.File
	file_pbentity_wisdom_language_proto_goTypes = nil
	file_pbentity_wisdom_language_proto_depIdxs = nil
}
