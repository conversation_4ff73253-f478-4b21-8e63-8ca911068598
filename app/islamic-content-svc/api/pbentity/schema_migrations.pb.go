// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/schema_migrations.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SchemaMigrations struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Version       int64                  `protobuf:"varint,1,opt,name=Version,proto3" json:"Version,omitempty"` //
	Dirty         int32                  `protobuf:"varint,2,opt,name=Dirty,proto3" json:"Dirty,omitempty"`     //
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaMigrations) Reset() {
	*x = SchemaMigrations{}
	mi := &file_pbentity_schema_migrations_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaMigrations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaMigrations) ProtoMessage() {}

func (x *SchemaMigrations) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_schema_migrations_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaMigrations.ProtoReflect.Descriptor instead.
func (*SchemaMigrations) Descriptor() ([]byte, []int) {
	return file_pbentity_schema_migrations_proto_rawDescGZIP(), []int{0}
}

func (x *SchemaMigrations) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *SchemaMigrations) GetDirty() int32 {
	if x != nil {
		return x.Dirty
	}
	return 0
}

var File_pbentity_schema_migrations_proto protoreflect.FileDescriptor

const file_pbentity_schema_migrations_proto_rawDesc = "" +
	"\n" +
	" pbentity/schema_migrations.proto\x12\bpbentity\"B\n" +
	"\x10SchemaMigrations\x12\x18\n" +
	"\aVersion\x18\x01 \x01(\x03R\aVersion\x12\x14\n" +
	"\x05Dirty\x18\x02 \x01(\x05R\x05DirtyB-Z+halalplus/app/user-account-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_schema_migrations_proto_rawDescOnce sync.Once
	file_pbentity_schema_migrations_proto_rawDescData []byte
)

func file_pbentity_schema_migrations_proto_rawDescGZIP() []byte {
	file_pbentity_schema_migrations_proto_rawDescOnce.Do(func() {
		file_pbentity_schema_migrations_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_schema_migrations_proto_rawDesc), len(file_pbentity_schema_migrations_proto_rawDesc)))
	})
	return file_pbentity_schema_migrations_proto_rawDescData
}

var file_pbentity_schema_migrations_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_schema_migrations_proto_goTypes = []any{
	(*SchemaMigrations)(nil), // 0: pbentity.SchemaMigrations
}
var file_pbentity_schema_migrations_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_schema_migrations_proto_init() }
func file_pbentity_schema_migrations_proto_init() {
	if File_pbentity_schema_migrations_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_schema_migrations_proto_rawDesc), len(file_pbentity_schema_migrations_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_schema_migrations_proto_goTypes,
		DependencyIndexes: file_pbentity_schema_migrations_proto_depIdxs,
		MessageInfos:      file_pbentity_schema_migrations_proto_msgTypes,
	}.Build()
	File_pbentity_schema_migrations_proto = out.File
	file_pbentity_schema_migrations_proto_goTypes = nil
	file_pbentity_schema_migrations_proto_depIdxs = nil
}
