// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/haji_urutan_content.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiUrutanContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	UrutanId      uint64                 `protobuf:"varint,2,opt,name=UrutanId,proto3" json:"UrutanId,omitempty" dc:"朝觐顺序ID，关联haji_urutan.id"`     // 朝觐顺序ID，关联haji_urutan.id
	LanguageId    uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	UrutanName    string                 `protobuf:"bytes,4,opt,name=UrutanName,proto3" json:"UrutanName,omitempty" dc:"朝觐仪式名称（最多60个字符）"`          // 朝觐仪式名称（最多60个字符）
	UrutanTime    string                 `protobuf:"bytes,5,opt,name=UrutanTime,proto3" json:"UrutanTime,omitempty" dc:"仪式时间"`                     // 仪式时间
	UrutanContent string                 `protobuf:"bytes,6,opt,name=UrutanContent,proto3" json:"UrutanContent,omitempty" dc:"仪式内容描述（富文本）"`        // 仪式内容描述（富文本）
	CreateTime    uint64                 `protobuf:"varint,7,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`             // 创建时间（毫秒时间戳）
	UpdateTime    uint64                 `protobuf:"varint,8,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`             // 更新时间（毫秒时间戳）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiUrutanContent) Reset() {
	*x = HajiUrutanContent{}
	mi := &file_pbentity_haji_urutan_content_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiUrutanContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiUrutanContent) ProtoMessage() {}

func (x *HajiUrutanContent) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_urutan_content_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiUrutanContent.ProtoReflect.Descriptor instead.
func (*HajiUrutanContent) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_urutan_content_proto_rawDescGZIP(), []int{0}
}

func (x *HajiUrutanContent) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiUrutanContent) GetUrutanId() uint64 {
	if x != nil {
		return x.UrutanId
	}
	return 0
}

func (x *HajiUrutanContent) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *HajiUrutanContent) GetUrutanName() string {
	if x != nil {
		return x.UrutanName
	}
	return ""
}

func (x *HajiUrutanContent) GetUrutanTime() string {
	if x != nil {
		return x.UrutanTime
	}
	return ""
}

func (x *HajiUrutanContent) GetUrutanContent() string {
	if x != nil {
		return x.UrutanContent
	}
	return ""
}

func (x *HajiUrutanContent) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiUrutanContent) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_urutan_content_proto protoreflect.FileDescriptor

const file_pbentity_haji_urutan_content_proto_rawDesc = "" +
	"\n" +
	"\"pbentity/haji_urutan_content.proto\x12\bpbentity\"\x85\x02\n" +
	"\x11HajiUrutanContent\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x1a\n" +
	"\bUrutanId\x18\x02 \x01(\x04R\bUrutanId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\x1e\n" +
	"\n" +
	"UrutanName\x18\x04 \x01(\tR\n" +
	"UrutanName\x12\x1e\n" +
	"\n" +
	"UrutanTime\x18\x05 \x01(\tR\n" +
	"UrutanTime\x12$\n" +
	"\rUrutanContent\x18\x06 \x01(\tR\rUrutanContent\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\a \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\b \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_haji_urutan_content_proto_rawDescOnce sync.Once
	file_pbentity_haji_urutan_content_proto_rawDescData []byte
)

func file_pbentity_haji_urutan_content_proto_rawDescGZIP() []byte {
	file_pbentity_haji_urutan_content_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_urutan_content_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_haji_urutan_content_proto_rawDesc), len(file_pbentity_haji_urutan_content_proto_rawDesc)))
	})
	return file_pbentity_haji_urutan_content_proto_rawDescData
}

var file_pbentity_haji_urutan_content_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_urutan_content_proto_goTypes = []any{
	(*HajiUrutanContent)(nil), // 0: pbentity.HajiUrutanContent
}
var file_pbentity_haji_urutan_content_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_urutan_content_proto_init() }
func file_pbentity_haji_urutan_content_proto_init() {
	if File_pbentity_haji_urutan_content_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_haji_urutan_content_proto_rawDesc), len(file_pbentity_haji_urutan_content_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_urutan_content_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_urutan_content_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_urutan_content_proto_msgTypes,
	}.Build()
	File_pbentity_haji_urutan_content_proto = out.File
	file_pbentity_haji_urutan_content_proto_goTypes = nil
	file_pbentity_haji_urutan_content_proto_depIdxs = nil
}
