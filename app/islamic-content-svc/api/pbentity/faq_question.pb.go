// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/faq_question.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaqQuestion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                            //
	IsZh          uint32                 `protobuf:"varint,2,opt,name=IsZh,proto3" json:"IsZh,omitempty" dc:"是否中文，0-否，1-是"`                      // 是否中文，0-否，1-是
	IsEn          uint32                 `protobuf:"varint,3,opt,name=IsEn,proto3" json:"IsEn,omitempty" dc:"是否英文，0-否，1-是"`                      // 是否英文，0-否，1-是
	IsId          uint32                 `protobuf:"varint,4,opt,name=IsId,proto3" json:"IsId,omitempty" dc:"是否印尼文，0-否，1-是"`                     // 是否印尼文，0-否，1-是
	FaqCateId     uint32                 `protobuf:"varint,5,opt,name=FaqCateId,proto3" json:"FaqCateId,omitempty"`                              //
	IsPublish     int32                  `protobuf:"varint,6,opt,name=IsPublish,proto3" json:"IsPublish,omitempty" dc:"发布状态 [ 0未发布 1已发布 2 已下线]"` // 发布状态 [ 0未发布 1已发布 2 已下线]
	Sort          int32                  `protobuf:"varint,7,opt,name=Sort,proto3" json:"Sort,omitempty" dc:"排序"`                                // 排序
	PublishTime   uint64                 `protobuf:"varint,8,opt,name=PublishTime,proto3" json:"PublishTime,omitempty" dc:"发布时间"`                // 发布时间
	Views         int32                  `protobuf:"varint,9,opt,name=Views,proto3" json:"Views,omitempty" dc:"浏览量"`                             // 浏览量
	CreateAccount string                 `protobuf:"bytes,10,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者"`             // 创建者
	UpdateAccount string                 `protobuf:"bytes,11,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者"`             // 更新者
	CreateTime    int64                  `protobuf:"varint,12,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`                           //
	UpdateTime    int64                  `protobuf:"varint,13,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty"`                           //
	DeleteTime    int64                  `protobuf:"varint,14,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty"`                           //
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqQuestion) Reset() {
	*x = FaqQuestion{}
	mi := &file_pbentity_faq_question_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqQuestion) ProtoMessage() {}

func (x *FaqQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_faq_question_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqQuestion.ProtoReflect.Descriptor instead.
func (*FaqQuestion) Descriptor() ([]byte, []int) {
	return file_pbentity_faq_question_proto_rawDescGZIP(), []int{0}
}

func (x *FaqQuestion) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqQuestion) GetIsZh() uint32 {
	if x != nil {
		return x.IsZh
	}
	return 0
}

func (x *FaqQuestion) GetIsEn() uint32 {
	if x != nil {
		return x.IsEn
	}
	return 0
}

func (x *FaqQuestion) GetIsId() uint32 {
	if x != nil {
		return x.IsId
	}
	return 0
}

func (x *FaqQuestion) GetFaqCateId() uint32 {
	if x != nil {
		return x.FaqCateId
	}
	return 0
}

func (x *FaqQuestion) GetIsPublish() int32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *FaqQuestion) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqQuestion) GetPublishTime() uint64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *FaqQuestion) GetViews() int32 {
	if x != nil {
		return x.Views
	}
	return 0
}

func (x *FaqQuestion) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *FaqQuestion) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *FaqQuestion) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *FaqQuestion) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *FaqQuestion) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_faq_question_proto protoreflect.FileDescriptor

const file_pbentity_faq_question_proto_rawDesc = "" +
	"\n" +
	"\x1bpbentity/faq_question.proto\x12\bpbentity\"\x8d\x03\n" +
	"\vFaqQuestion\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04IsZh\x18\x02 \x01(\rR\x04IsZh\x12\x12\n" +
	"\x04IsEn\x18\x03 \x01(\rR\x04IsEn\x12\x12\n" +
	"\x04IsId\x18\x04 \x01(\rR\x04IsId\x12\x1c\n" +
	"\tFaqCateId\x18\x05 \x01(\rR\tFaqCateId\x12\x1c\n" +
	"\tIsPublish\x18\x06 \x01(\x05R\tIsPublish\x12\x12\n" +
	"\x04Sort\x18\a \x01(\x05R\x04Sort\x12 \n" +
	"\vPublishTime\x18\b \x01(\x04R\vPublishTime\x12\x14\n" +
	"\x05Views\x18\t \x01(\x05R\x05Views\x12$\n" +
	"\rCreateAccount\x18\n" +
	" \x01(\tR\rCreateAccount\x12$\n" +
	"\rUpdateAccount\x18\v \x01(\tR\rUpdateAccount\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\f \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\r \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\x0e \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_faq_question_proto_rawDescOnce sync.Once
	file_pbentity_faq_question_proto_rawDescData []byte
)

func file_pbentity_faq_question_proto_rawDescGZIP() []byte {
	file_pbentity_faq_question_proto_rawDescOnce.Do(func() {
		file_pbentity_faq_question_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_faq_question_proto_rawDesc), len(file_pbentity_faq_question_proto_rawDesc)))
	})
	return file_pbentity_faq_question_proto_rawDescData
}

var file_pbentity_faq_question_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_faq_question_proto_goTypes = []any{
	(*FaqQuestion)(nil), // 0: pbentity.FaqQuestion
}
var file_pbentity_faq_question_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_faq_question_proto_init() }
func file_pbentity_faq_question_proto_init() {
	if File_pbentity_faq_question_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_faq_question_proto_rawDesc), len(file_pbentity_faq_question_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_faq_question_proto_goTypes,
		DependencyIndexes: file_pbentity_faq_question_proto_depIdxs,
		MessageInfos:      file_pbentity_faq_question_proto_msgTypes,
	}.Build()
	File_pbentity_faq_question_proto = out.File
	file_pbentity_faq_question_proto_goTypes = nil
	file_pbentity_faq_question_proto_depIdxs = nil
}
