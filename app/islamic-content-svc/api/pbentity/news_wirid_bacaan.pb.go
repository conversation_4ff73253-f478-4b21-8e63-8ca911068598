// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/news_wirid_bacaan.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsWiridBacaan struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                     //
	WiridId       uint32                 `protobuf:"varint,2,opt,name=WiridId,proto3" json:"WiridId,omitempty" dc:"wirid_id"`             // wirid_id
	Name          string                 `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty" dc:"名称"`                          // 名称
	Content1      string                 `protobuf:"bytes,4,opt,name=Content1,proto3" json:"Content1,omitempty" dc:"内容1"`                 // 内容1
	Content2      string                 `protobuf:"bytes,5,opt,name=Content2,proto3" json:"Content2,omitempty" dc:"内容2"`                 // 内容2
	Content3      string                 `protobuf:"bytes,6,opt,name=Content3,proto3" json:"Content3,omitempty" dc:"内容3"`                 // 内容3
	CreateTime    int64                  `protobuf:"varint,7,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime    int64                  `protobuf:"varint,8,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
	Pid           int32                  `protobuf:"varint,9,opt,name=Pid,proto3" json:"Pid,omitempty" dc:"父级id"`                         // 父级id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsWiridBacaan) Reset() {
	*x = NewsWiridBacaan{}
	mi := &file_pbentity_news_wirid_bacaan_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsWiridBacaan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsWiridBacaan) ProtoMessage() {}

func (x *NewsWiridBacaan) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_wirid_bacaan_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsWiridBacaan.ProtoReflect.Descriptor instead.
func (*NewsWiridBacaan) Descriptor() ([]byte, []int) {
	return file_pbentity_news_wirid_bacaan_proto_rawDescGZIP(), []int{0}
}

func (x *NewsWiridBacaan) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsWiridBacaan) GetWiridId() uint32 {
	if x != nil {
		return x.WiridId
	}
	return 0
}

func (x *NewsWiridBacaan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NewsWiridBacaan) GetContent1() string {
	if x != nil {
		return x.Content1
	}
	return ""
}

func (x *NewsWiridBacaan) GetContent2() string {
	if x != nil {
		return x.Content2
	}
	return ""
}

func (x *NewsWiridBacaan) GetContent3() string {
	if x != nil {
		return x.Content3
	}
	return ""
}

func (x *NewsWiridBacaan) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsWiridBacaan) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsWiridBacaan) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

var File_pbentity_news_wirid_bacaan_proto protoreflect.FileDescriptor

const file_pbentity_news_wirid_bacaan_proto_rawDesc = "" +
	"\n" +
	" pbentity/news_wirid_bacaan.proto\x12\bpbentity\"\xf5\x01\n" +
	"\x0fNewsWiridBacaan\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x18\n" +
	"\aWiridId\x18\x02 \x01(\rR\aWiridId\x12\x12\n" +
	"\x04Name\x18\x03 \x01(\tR\x04Name\x12\x1a\n" +
	"\bContent1\x18\x04 \x01(\tR\bContent1\x12\x1a\n" +
	"\bContent2\x18\x05 \x01(\tR\bContent2\x12\x1a\n" +
	"\bContent3\x18\x06 \x01(\tR\bContent3\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\a \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\b \x01(\x03R\n" +
	"UpdateTime\x12\x10\n" +
	"\x03Pid\x18\t \x01(\x05R\x03PidB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_wirid_bacaan_proto_rawDescOnce sync.Once
	file_pbentity_news_wirid_bacaan_proto_rawDescData []byte
)

func file_pbentity_news_wirid_bacaan_proto_rawDescGZIP() []byte {
	file_pbentity_news_wirid_bacaan_proto_rawDescOnce.Do(func() {
		file_pbentity_news_wirid_bacaan_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_wirid_bacaan_proto_rawDesc), len(file_pbentity_news_wirid_bacaan_proto_rawDesc)))
	})
	return file_pbentity_news_wirid_bacaan_proto_rawDescData
}

var file_pbentity_news_wirid_bacaan_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_wirid_bacaan_proto_goTypes = []any{
	(*NewsWiridBacaan)(nil), // 0: pbentity.NewsWiridBacaan
}
var file_pbentity_news_wirid_bacaan_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_wirid_bacaan_proto_init() }
func file_pbentity_news_wirid_bacaan_proto_init() {
	if File_pbentity_news_wirid_bacaan_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_wirid_bacaan_proto_rawDesc), len(file_pbentity_news_wirid_bacaan_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_wirid_bacaan_proto_goTypes,
		DependencyIndexes: file_pbentity_news_wirid_bacaan_proto_depIdxs,
		MessageInfos:      file_pbentity_news_wirid_bacaan_proto_msgTypes,
	}.Build()
	File_pbentity_news_wirid_bacaan_proto = out.File
	file_pbentity_news_wirid_bacaan_proto_goTypes = nil
	file_pbentity_news_wirid_bacaan_proto_depIdxs = nil
}
