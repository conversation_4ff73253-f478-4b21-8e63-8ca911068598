// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/faq_show_position.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaqShowPosition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                    //
	FaqQuestionId uint32                 `protobuf:"varint,2,opt,name=FaqQuestionId,proto3" json:"FaqQuestionId,omitempty" dc:"faq id和"` // faq id和
	PositionId    int32                  `protobuf:"varint,3,opt,name=PositionId,proto3" json:"PositionId,omitempty" dc:"位置id"`          // 位置id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqShowPosition) Reset() {
	*x = FaqShowPosition{}
	mi := &file_pbentity_faq_show_position_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqShowPosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqShowPosition) ProtoMessage() {}

func (x *FaqShowPosition) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_faq_show_position_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqShowPosition.ProtoReflect.Descriptor instead.
func (*FaqShowPosition) Descriptor() ([]byte, []int) {
	return file_pbentity_faq_show_position_proto_rawDescGZIP(), []int{0}
}

func (x *FaqShowPosition) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqShowPosition) GetFaqQuestionId() uint32 {
	if x != nil {
		return x.FaqQuestionId
	}
	return 0
}

func (x *FaqShowPosition) GetPositionId() int32 {
	if x != nil {
		return x.PositionId
	}
	return 0
}

var File_pbentity_faq_show_position_proto protoreflect.FileDescriptor

const file_pbentity_faq_show_position_proto_rawDesc = "" +
	"\n" +
	" pbentity/faq_show_position.proto\x12\bpbentity\"g\n" +
	"\x0fFaqShowPosition\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12$\n" +
	"\rFaqQuestionId\x18\x02 \x01(\rR\rFaqQuestionId\x12\x1e\n" +
	"\n" +
	"PositionId\x18\x03 \x01(\x05R\n" +
	"PositionIdB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_faq_show_position_proto_rawDescOnce sync.Once
	file_pbentity_faq_show_position_proto_rawDescData []byte
)

func file_pbentity_faq_show_position_proto_rawDescGZIP() []byte {
	file_pbentity_faq_show_position_proto_rawDescOnce.Do(func() {
		file_pbentity_faq_show_position_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_faq_show_position_proto_rawDesc), len(file_pbentity_faq_show_position_proto_rawDesc)))
	})
	return file_pbentity_faq_show_position_proto_rawDescData
}

var file_pbentity_faq_show_position_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_faq_show_position_proto_goTypes = []any{
	(*FaqShowPosition)(nil), // 0: pbentity.FaqShowPosition
}
var file_pbentity_faq_show_position_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_faq_show_position_proto_init() }
func file_pbentity_faq_show_position_proto_init() {
	if File_pbentity_faq_show_position_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_faq_show_position_proto_rawDesc), len(file_pbentity_faq_show_position_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_faq_show_position_proto_goTypes,
		DependencyIndexes: file_pbentity_faq_show_position_proto_depIdxs,
		MessageInfos:      file_pbentity_faq_show_position_proto_msgTypes,
	}.Build()
	File_pbentity_faq_show_position_proto = out.File
	file_pbentity_faq_show_position_proto_goTypes = nil
	file_pbentity_faq_show_position_proto_depIdxs = nil
}
