// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/umrah_landmark.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UmrahLandmark struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	TypeId        uint64                 `protobuf:"varint,2,opt,name=TypeId,proto3" json:"TypeId,omitempty" dc:"地标类型ID，关联umrah_landmark_type.id"` // 地标类型ID，关联umrah_landmark_type.id
	InnerType     string                 `protobuf:"bytes,3,opt,name=InnerType,proto3" json:"InnerType,omitempty" dc:"内部类型: (destinasi, tokoh)"`   // 内部类型: (destinasi, tokoh)
	Latitude      string                 `protobuf:"bytes,4,opt,name=Latitude,proto3" json:"Latitude,omitempty" dc:"纬度"`                           // 纬度
	Longitude     string                 `protobuf:"bytes,5,opt,name=Longitude,proto3" json:"Longitude,omitempty" dc:"经度"`                         // 经度
	ImageUrl      string                 `protobuf:"bytes,6,opt,name=ImageUrl,proto3" json:"ImageUrl,omitempty" dc:"图片URL"`                        // 图片URL
	SortOrder     uint32                 `protobuf:"varint,7,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序值，数字越小排序越靠前"`             // 排序值，数字越小排序越靠前
	CreateTime    uint64                 `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`             // 创建时间（毫秒时间戳）
	UpdateTime    uint64                 `protobuf:"varint,9,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`             // 更新时间（毫秒时间戳）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahLandmark) Reset() {
	*x = UmrahLandmark{}
	mi := &file_pbentity_umrah_landmark_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahLandmark) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahLandmark) ProtoMessage() {}

func (x *UmrahLandmark) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_umrah_landmark_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahLandmark.ProtoReflect.Descriptor instead.
func (*UmrahLandmark) Descriptor() ([]byte, []int) {
	return file_pbentity_umrah_landmark_proto_rawDescGZIP(), []int{0}
}

func (x *UmrahLandmark) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahLandmark) GetTypeId() uint64 {
	if x != nil {
		return x.TypeId
	}
	return 0
}

func (x *UmrahLandmark) GetInnerType() string {
	if x != nil {
		return x.InnerType
	}
	return ""
}

func (x *UmrahLandmark) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *UmrahLandmark) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *UmrahLandmark) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *UmrahLandmark) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *UmrahLandmark) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UmrahLandmark) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_umrah_landmark_proto protoreflect.FileDescriptor

const file_pbentity_umrah_landmark_proto_rawDesc = "" +
	"\n" +
	"\x1dpbentity/umrah_landmark.proto\x12\bpbentity\"\x89\x02\n" +
	"\rUmrahLandmark\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x16\n" +
	"\x06TypeId\x18\x02 \x01(\x04R\x06TypeId\x12\x1c\n" +
	"\tInnerType\x18\x03 \x01(\tR\tInnerType\x12\x1a\n" +
	"\bLatitude\x18\x04 \x01(\tR\bLatitude\x12\x1c\n" +
	"\tLongitude\x18\x05 \x01(\tR\tLongitude\x12\x1a\n" +
	"\bImageUrl\x18\x06 \x01(\tR\bImageUrl\x12\x1c\n" +
	"\tSortOrder\x18\a \x01(\rR\tSortOrder\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\b \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\t \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_umrah_landmark_proto_rawDescOnce sync.Once
	file_pbentity_umrah_landmark_proto_rawDescData []byte
)

func file_pbentity_umrah_landmark_proto_rawDescGZIP() []byte {
	file_pbentity_umrah_landmark_proto_rawDescOnce.Do(func() {
		file_pbentity_umrah_landmark_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_umrah_landmark_proto_rawDesc), len(file_pbentity_umrah_landmark_proto_rawDesc)))
	})
	return file_pbentity_umrah_landmark_proto_rawDescData
}

var file_pbentity_umrah_landmark_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_umrah_landmark_proto_goTypes = []any{
	(*UmrahLandmark)(nil), // 0: pbentity.UmrahLandmark
}
var file_pbentity_umrah_landmark_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_umrah_landmark_proto_init() }
func file_pbentity_umrah_landmark_proto_init() {
	if File_pbentity_umrah_landmark_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_umrah_landmark_proto_rawDesc), len(file_pbentity_umrah_landmark_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_umrah_landmark_proto_goTypes,
		DependencyIndexes: file_pbentity_umrah_landmark_proto_depIdxs,
		MessageInfos:      file_pbentity_umrah_landmark_proto_msgTypes,
	}.Build()
	File_pbentity_umrah_landmark_proto = out.File
	file_pbentity_umrah_landmark_proto_goTypes = nil
	file_pbentity_umrah_landmark_proto_depIdxs = nil
}
