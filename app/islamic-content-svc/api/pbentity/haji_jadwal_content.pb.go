// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/haji_jadwal_content.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiJadwalContent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	JadwalId       uint64                 `protobuf:"varint,2,opt,name=JadwalId,proto3" json:"JadwalId,omitempty" dc:"朝觐时间表ID，关联haji_jadwal.id"`    // 朝觐时间表ID，关联haji_jadwal.id
	LanguageId     uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	TimeInfo       string                 `protobuf:"bytes,4,opt,name=TimeInfo,proto3" json:"TimeInfo,omitempty" dc:"手动输入的时间信息"`                    // 手动输入的时间信息
	EventSummary   string                 `protobuf:"bytes,5,opt,name=EventSummary,proto3" json:"EventSummary,omitempty" dc:"事件简述"`                 // 事件简述
	AdditionalInfo string                 `protobuf:"bytes,6,opt,name=AdditionalInfo,proto3" json:"AdditionalInfo,omitempty" dc:"附加信息"`             // 附加信息
	ArticleText    string                 `protobuf:"bytes,7,opt,name=ArticleText,proto3" json:"ArticleText,omitempty" dc:"文章详情（副文本）"`              // 文章详情（副文本）
	CreateTime     uint64                 `protobuf:"varint,8,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`             // 创建时间（毫秒时间戳）
	UpdateTime     uint64                 `protobuf:"varint,9,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`             // 更新时间（毫秒时间戳）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HajiJadwalContent) Reset() {
	*x = HajiJadwalContent{}
	mi := &file_pbentity_haji_jadwal_content_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiJadwalContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiJadwalContent) ProtoMessage() {}

func (x *HajiJadwalContent) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_jadwal_content_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiJadwalContent.ProtoReflect.Descriptor instead.
func (*HajiJadwalContent) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_jadwal_content_proto_rawDescGZIP(), []int{0}
}

func (x *HajiJadwalContent) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiJadwalContent) GetJadwalId() uint64 {
	if x != nil {
		return x.JadwalId
	}
	return 0
}

func (x *HajiJadwalContent) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *HajiJadwalContent) GetTimeInfo() string {
	if x != nil {
		return x.TimeInfo
	}
	return ""
}

func (x *HajiJadwalContent) GetEventSummary() string {
	if x != nil {
		return x.EventSummary
	}
	return ""
}

func (x *HajiJadwalContent) GetAdditionalInfo() string {
	if x != nil {
		return x.AdditionalInfo
	}
	return ""
}

func (x *HajiJadwalContent) GetArticleText() string {
	if x != nil {
		return x.ArticleText
	}
	return ""
}

func (x *HajiJadwalContent) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiJadwalContent) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_jadwal_content_proto protoreflect.FileDescriptor

const file_pbentity_haji_jadwal_content_proto_rawDesc = "" +
	"\n" +
	"\"pbentity/haji_jadwal_content.proto\x12\bpbentity\"\xa9\x02\n" +
	"\x11HajiJadwalContent\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x1a\n" +
	"\bJadwalId\x18\x02 \x01(\x04R\bJadwalId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\x1a\n" +
	"\bTimeInfo\x18\x04 \x01(\tR\bTimeInfo\x12\"\n" +
	"\fEventSummary\x18\x05 \x01(\tR\fEventSummary\x12&\n" +
	"\x0eAdditionalInfo\x18\x06 \x01(\tR\x0eAdditionalInfo\x12 \n" +
	"\vArticleText\x18\a \x01(\tR\vArticleText\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\b \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\t \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_haji_jadwal_content_proto_rawDescOnce sync.Once
	file_pbentity_haji_jadwal_content_proto_rawDescData []byte
)

func file_pbentity_haji_jadwal_content_proto_rawDescGZIP() []byte {
	file_pbentity_haji_jadwal_content_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_jadwal_content_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_haji_jadwal_content_proto_rawDesc), len(file_pbentity_haji_jadwal_content_proto_rawDesc)))
	})
	return file_pbentity_haji_jadwal_content_proto_rawDescData
}

var file_pbentity_haji_jadwal_content_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_jadwal_content_proto_goTypes = []any{
	(*HajiJadwalContent)(nil), // 0: pbentity.HajiJadwalContent
}
var file_pbentity_haji_jadwal_content_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_jadwal_content_proto_init() }
func file_pbentity_haji_jadwal_content_proto_init() {
	if File_pbentity_haji_jadwal_content_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_haji_jadwal_content_proto_rawDesc), len(file_pbentity_haji_jadwal_content_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_jadwal_content_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_jadwal_content_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_jadwal_content_proto_msgTypes,
	}.Build()
	File_pbentity_haji_jadwal_content_proto = out.File
	file_pbentity_haji_jadwal_content_proto_goTypes = nil
	file_pbentity_haji_jadwal_content_proto_depIdxs = nil
}
