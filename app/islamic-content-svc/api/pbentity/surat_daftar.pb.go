// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/surat_daftar.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SuratDaftar struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                   //
	Nomor         int32                  `protobuf:"varint,2,opt,name=Nomor,proto3" json:"Nomor,omitempty" dc:"章节编号 (1-114)"`           // 章节编号 (1-114)
	Nama          string                 `protobuf:"bytes,3,opt,name=Nama,proto3" json:"Nama,omitempty" dc:"阿拉伯语章节名"`                   // 阿拉伯语章节名
	NamaLatin     string                 `protobuf:"bytes,4,opt,name=NamaLatin,proto3" json:"NamaLatin,omitempty" dc:"拉丁化章节名"`          // 拉丁化章节名
	JumlahAyat    int32                  `protobuf:"varint,5,opt,name=JumlahAyat,proto3" json:"JumlahAyat,omitempty" dc:"经文数量"`         // 经文数量
	TempatTurun   string                 `protobuf:"bytes,6,opt,name=TempatTurun,proto3" json:"TempatTurun,omitempty" dc:"降示地点"`        // 降示地点
	Arti          string                 `protobuf:"bytes,7,opt,name=Arti,proto3" json:"Arti,omitempty" dc:"章节含义"`                      // 章节含义
	Deskripsi     string                 `protobuf:"bytes,8,opt,name=Deskripsi,proto3" json:"Deskripsi,omitempty" dc:"章节描述"`            // 章节描述
	Audio         string                 `protobuf:"bytes,9,opt,name=Audio,proto3" json:"Audio,omitempty" dc:"音频文件URL"`                 // 音频文件URL
	Status        int32                  `protobuf:"varint,10,opt,name=Status,proto3" json:"Status,omitempty" dc:"状态标识"`                // 状态标识
	IsPopular     int32                  `protobuf:"varint,11,opt,name=IsPopular,proto3" json:"IsPopular,omitempty" dc:"是否热门章节 0否 1是"`  // 是否热门章节 0否 1是
	CreatedTime   uint64                 `protobuf:"varint,12,opt,name=CreatedTime,proto3" json:"CreatedTime,omitempty" dc:"创建时间戳(毫秒)"` // 创建时间戳(毫秒)
	UpdatedTime   uint64                 `protobuf:"varint,13,opt,name=UpdatedTime,proto3" json:"UpdatedTime,omitempty" dc:"修改时间戳(毫秒)"` // 修改时间戳(毫秒)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SuratDaftar) Reset() {
	*x = SuratDaftar{}
	mi := &file_pbentity_surat_daftar_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SuratDaftar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SuratDaftar) ProtoMessage() {}

func (x *SuratDaftar) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surat_daftar_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SuratDaftar.ProtoReflect.Descriptor instead.
func (*SuratDaftar) Descriptor() ([]byte, []int) {
	return file_pbentity_surat_daftar_proto_rawDescGZIP(), []int{0}
}

func (x *SuratDaftar) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SuratDaftar) GetNomor() int32 {
	if x != nil {
		return x.Nomor
	}
	return 0
}

func (x *SuratDaftar) GetNama() string {
	if x != nil {
		return x.Nama
	}
	return ""
}

func (x *SuratDaftar) GetNamaLatin() string {
	if x != nil {
		return x.NamaLatin
	}
	return ""
}

func (x *SuratDaftar) GetJumlahAyat() int32 {
	if x != nil {
		return x.JumlahAyat
	}
	return 0
}

func (x *SuratDaftar) GetTempatTurun() string {
	if x != nil {
		return x.TempatTurun
	}
	return ""
}

func (x *SuratDaftar) GetArti() string {
	if x != nil {
		return x.Arti
	}
	return ""
}

func (x *SuratDaftar) GetDeskripsi() string {
	if x != nil {
		return x.Deskripsi
	}
	return ""
}

func (x *SuratDaftar) GetAudio() string {
	if x != nil {
		return x.Audio
	}
	return ""
}

func (x *SuratDaftar) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *SuratDaftar) GetIsPopular() int32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

func (x *SuratDaftar) GetCreatedTime() uint64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *SuratDaftar) GetUpdatedTime() uint64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

var File_pbentity_surat_daftar_proto protoreflect.FileDescriptor

const file_pbentity_surat_daftar_proto_rawDesc = "" +
	"\n" +
	"\x1bpbentity/surat_daftar.proto\x12\bpbentity\"\xe9\x02\n" +
	"\vSuratDaftar\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\x14\n" +
	"\x05Nomor\x18\x02 \x01(\x05R\x05Nomor\x12\x12\n" +
	"\x04Nama\x18\x03 \x01(\tR\x04Nama\x12\x1c\n" +
	"\tNamaLatin\x18\x04 \x01(\tR\tNamaLatin\x12\x1e\n" +
	"\n" +
	"JumlahAyat\x18\x05 \x01(\x05R\n" +
	"JumlahAyat\x12 \n" +
	"\vTempatTurun\x18\x06 \x01(\tR\vTempatTurun\x12\x12\n" +
	"\x04Arti\x18\a \x01(\tR\x04Arti\x12\x1c\n" +
	"\tDeskripsi\x18\b \x01(\tR\tDeskripsi\x12\x14\n" +
	"\x05Audio\x18\t \x01(\tR\x05Audio\x12\x16\n" +
	"\x06Status\x18\n" +
	" \x01(\x05R\x06Status\x12\x1c\n" +
	"\tIsPopular\x18\v \x01(\x05R\tIsPopular\x12 \n" +
	"\vCreatedTime\x18\f \x01(\x04R\vCreatedTime\x12 \n" +
	"\vUpdatedTime\x18\r \x01(\x04R\vUpdatedTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_surat_daftar_proto_rawDescOnce sync.Once
	file_pbentity_surat_daftar_proto_rawDescData []byte
)

func file_pbentity_surat_daftar_proto_rawDescGZIP() []byte {
	file_pbentity_surat_daftar_proto_rawDescOnce.Do(func() {
		file_pbentity_surat_daftar_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_surat_daftar_proto_rawDesc), len(file_pbentity_surat_daftar_proto_rawDesc)))
	})
	return file_pbentity_surat_daftar_proto_rawDescData
}

var file_pbentity_surat_daftar_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surat_daftar_proto_goTypes = []any{
	(*SuratDaftar)(nil), // 0: pbentity.SuratDaftar
}
var file_pbentity_surat_daftar_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_surat_daftar_proto_init() }
func file_pbentity_surat_daftar_proto_init() {
	if File_pbentity_surat_daftar_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_surat_daftar_proto_rawDesc), len(file_pbentity_surat_daftar_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surat_daftar_proto_goTypes,
		DependencyIndexes: file_pbentity_surat_daftar_proto_depIdxs,
		MessageInfos:      file_pbentity_surat_daftar_proto_msgTypes,
	}.Build()
	File_pbentity_surat_daftar_proto = out.File
	file_pbentity_surat_daftar_proto_goTypes = nil
	file_pbentity_surat_daftar_proto_depIdxs = nil
}
