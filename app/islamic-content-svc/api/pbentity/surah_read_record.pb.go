// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/surah_read_record.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SurahReadRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                     //
	UserId        uint32                 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户id"`                   // 用户id
	AyahId        uint32                 `protobuf:"varint,3,opt,name=AyahId,proto3" json:"AyahId,omitempty" dc:"ayah_id节id"`             // ayah_id节id
	SurahName     string                 `protobuf:"bytes,4,opt,name=SurahName,proto3" json:"SurahName,omitempty" dc:"名称"`                // 名称
	IsUserOp      uint32                 `protobuf:"varint,5,opt,name=IsUserOp,proto3" json:"IsUserOp,omitempty" dc:"是否用户操作，1是 0否"`       // 是否用户操作，1是 0否
	CreateTime    int64                  `protobuf:"varint,6,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime    int64                  `protobuf:"varint,7,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahReadRecord) Reset() {
	*x = SurahReadRecord{}
	mi := &file_pbentity_surah_read_record_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahReadRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahReadRecord) ProtoMessage() {}

func (x *SurahReadRecord) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_surah_read_record_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahReadRecord.ProtoReflect.Descriptor instead.
func (*SurahReadRecord) Descriptor() ([]byte, []int) {
	return file_pbentity_surah_read_record_proto_rawDescGZIP(), []int{0}
}

func (x *SurahReadRecord) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahReadRecord) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *SurahReadRecord) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *SurahReadRecord) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *SurahReadRecord) GetIsUserOp() uint32 {
	if x != nil {
		return x.IsUserOp
	}
	return 0
}

func (x *SurahReadRecord) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SurahReadRecord) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_surah_read_record_proto protoreflect.FileDescriptor

const file_pbentity_surah_read_record_proto_rawDesc = "" +
	"\n" +
	" pbentity/surah_read_record.proto\x12\bpbentity\"\xcb\x01\n" +
	"\x0fSurahReadRecord\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x16\n" +
	"\x06UserId\x18\x02 \x01(\rR\x06UserId\x12\x16\n" +
	"\x06AyahId\x18\x03 \x01(\rR\x06AyahId\x12\x1c\n" +
	"\tSurahName\x18\x04 \x01(\tR\tSurahName\x12\x1a\n" +
	"\bIsUserOp\x18\x05 \x01(\rR\bIsUserOp\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x06 \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\a \x01(\x03R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_surah_read_record_proto_rawDescOnce sync.Once
	file_pbentity_surah_read_record_proto_rawDescData []byte
)

func file_pbentity_surah_read_record_proto_rawDescGZIP() []byte {
	file_pbentity_surah_read_record_proto_rawDescOnce.Do(func() {
		file_pbentity_surah_read_record_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_surah_read_record_proto_rawDesc), len(file_pbentity_surah_read_record_proto_rawDesc)))
	})
	return file_pbentity_surah_read_record_proto_rawDescData
}

var file_pbentity_surah_read_record_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_surah_read_record_proto_goTypes = []any{
	(*SurahReadRecord)(nil), // 0: pbentity.SurahReadRecord
}
var file_pbentity_surah_read_record_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_surah_read_record_proto_init() }
func file_pbentity_surah_read_record_proto_init() {
	if File_pbentity_surah_read_record_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_surah_read_record_proto_rawDesc), len(file_pbentity_surah_read_record_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_surah_read_record_proto_goTypes,
		DependencyIndexes: file_pbentity_surah_read_record_proto_depIdxs,
		MessageInfos:      file_pbentity_surah_read_record_proto_msgTypes,
	}.Build()
	File_pbentity_surah_read_record_proto = out.File
	file_pbentity_surah_read_record_proto_goTypes = nil
	file_pbentity_surah_read_record_proto_depIdxs = nil
}
