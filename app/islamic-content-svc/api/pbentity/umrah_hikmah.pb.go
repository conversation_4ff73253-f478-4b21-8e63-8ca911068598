// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/umrah_hikmah.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UmrahHikmah struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                 // 主键ID
	ArticleId     uint64                 `protobuf:"varint,2,opt,name=ArticleId,proto3" json:"ArticleId,omitempty" dc:"文章ID，关联news_article.id"` // 文章ID，关联news_article.id
	SortOrder     uint32                 `protobuf:"varint,3,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序值，数字越小排序越靠前"`          // 排序值，数字越小排序越靠前
	CreateTime    uint64                 `protobuf:"varint,4,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`          // 创建时间（毫秒时间戳）
	UpdateTime    uint64                 `protobuf:"varint,5,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`          // 更新时间（毫秒时间戳）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UmrahHikmah) Reset() {
	*x = UmrahHikmah{}
	mi := &file_pbentity_umrah_hikmah_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UmrahHikmah) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UmrahHikmah) ProtoMessage() {}

func (x *UmrahHikmah) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_umrah_hikmah_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UmrahHikmah.ProtoReflect.Descriptor instead.
func (*UmrahHikmah) Descriptor() ([]byte, []int) {
	return file_pbentity_umrah_hikmah_proto_rawDescGZIP(), []int{0}
}

func (x *UmrahHikmah) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UmrahHikmah) GetArticleId() uint64 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *UmrahHikmah) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *UmrahHikmah) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UmrahHikmah) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_umrah_hikmah_proto protoreflect.FileDescriptor

const file_pbentity_umrah_hikmah_proto_rawDesc = "" +
	"\n" +
	"\x1bpbentity/umrah_hikmah.proto\x12\bpbentity\"\x99\x01\n" +
	"\vUmrahHikmah\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x1c\n" +
	"\tArticleId\x18\x02 \x01(\x04R\tArticleId\x12\x1c\n" +
	"\tSortOrder\x18\x03 \x01(\rR\tSortOrder\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x04 \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x05 \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_umrah_hikmah_proto_rawDescOnce sync.Once
	file_pbentity_umrah_hikmah_proto_rawDescData []byte
)

func file_pbentity_umrah_hikmah_proto_rawDescGZIP() []byte {
	file_pbentity_umrah_hikmah_proto_rawDescOnce.Do(func() {
		file_pbentity_umrah_hikmah_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_umrah_hikmah_proto_rawDesc), len(file_pbentity_umrah_hikmah_proto_rawDesc)))
	})
	return file_pbentity_umrah_hikmah_proto_rawDescData
}

var file_pbentity_umrah_hikmah_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_umrah_hikmah_proto_goTypes = []any{
	(*UmrahHikmah)(nil), // 0: pbentity.UmrahHikmah
}
var file_pbentity_umrah_hikmah_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_umrah_hikmah_proto_init() }
func file_pbentity_umrah_hikmah_proto_init() {
	if File_pbentity_umrah_hikmah_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_umrah_hikmah_proto_rawDesc), len(file_pbentity_umrah_hikmah_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_umrah_hikmah_proto_goTypes,
		DependencyIndexes: file_pbentity_umrah_hikmah_proto_depIdxs,
		MessageInfos:      file_pbentity_umrah_hikmah_proto_msgTypes,
	}.Build()
	File_pbentity_umrah_hikmah_proto = out.File
	file_pbentity_umrah_hikmah_proto_goTypes = nil
	file_pbentity_umrah_hikmah_proto_depIdxs = nil
}
