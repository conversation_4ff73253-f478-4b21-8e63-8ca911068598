// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/feedback.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Feedback struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int32                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                                //
	FeedbackType     int32                  `protobuf:"varint,2,opt,name=FeedbackType,proto3" json:"FeedbackType,omitempty" dc:"反馈类型 1 系统故障 2 功能建议"`    // 反馈类型 1 系统故障 2 功能建议
	UserId           uint32                 `protobuf:"varint,3,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户id"`                              // 用户id
	UserAccount      string                 `protobuf:"bytes,4,opt,name=UserAccount,proto3" json:"UserAccount,omitempty" dc:"用户账号"`                     // 用户账号
	Images           string                 `protobuf:"bytes,5,opt,name=Images,proto3" json:"Images,omitempty" dc:"反馈图片 json字符串"`                       // 反馈图片 json字符串
	Desc             string                 `protobuf:"bytes,6,opt,name=Desc,proto3" json:"Desc,omitempty" dc:"反馈内容"`                                   // 反馈内容
	FeedbackTime     int64                  `protobuf:"varint,7,opt,name=FeedbackTime,proto3" json:"FeedbackTime,omitempty" dc:"反馈时间ms"`                // 反馈时间ms
	FeedbackStatus   int32                  `protobuf:"varint,8,opt,name=FeedbackStatus,proto3" json:"FeedbackStatus,omitempty" dc:"反馈状态 1 未处理 2已处理"`   // 反馈状态 1 未处理 2已处理
	FeedbackResult   int32                  `protobuf:"varint,9,opt,name=FeedbackResult,proto3" json:"FeedbackResult,omitempty" dc:"反馈结果 1 无效反馈 2有效反馈"` // 反馈结果 1 无效反馈 2有效反馈
	CompleteTime     int64                  `protobuf:"varint,10,opt,name=CompleteTime,proto3" json:"CompleteTime,omitempty" dc:"处理时间ms"`               // 处理时间ms
	CompleteRemark   string                 `protobuf:"bytes,11,opt,name=CompleteRemark,proto3" json:"CompleteRemark,omitempty" dc:"处理备注"`              // 处理备注
	CompleteAccount  string                 `protobuf:"bytes,12,opt,name=CompleteAccount,proto3" json:"CompleteAccount,omitempty" dc:"处理人"`             // 处理人
	CompleteNickName string                 `protobuf:"bytes,13,opt,name=CompleteNickName,proto3" json:"CompleteNickName,omitempty" dc:"处理人昵称"`         // 处理人昵称
	CreateTime       int64                  `protobuf:"varint,14,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`                     // 创建时间
	UpdateTime       int64                  `protobuf:"varint,15,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间"`                     // 更新时间
	DeleteTime       int64                  `protobuf:"varint,16,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`                     // 删除时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Feedback) Reset() {
	*x = Feedback{}
	mi := &file_pbentity_feedback_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Feedback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feedback) ProtoMessage() {}

func (x *Feedback) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_feedback_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feedback.ProtoReflect.Descriptor instead.
func (*Feedback) Descriptor() ([]byte, []int) {
	return file_pbentity_feedback_proto_rawDescGZIP(), []int{0}
}

func (x *Feedback) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Feedback) GetFeedbackType() int32 {
	if x != nil {
		return x.FeedbackType
	}
	return 0
}

func (x *Feedback) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *Feedback) GetUserAccount() string {
	if x != nil {
		return x.UserAccount
	}
	return ""
}

func (x *Feedback) GetImages() string {
	if x != nil {
		return x.Images
	}
	return ""
}

func (x *Feedback) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *Feedback) GetFeedbackTime() int64 {
	if x != nil {
		return x.FeedbackTime
	}
	return 0
}

func (x *Feedback) GetFeedbackStatus() int32 {
	if x != nil {
		return x.FeedbackStatus
	}
	return 0
}

func (x *Feedback) GetFeedbackResult() int32 {
	if x != nil {
		return x.FeedbackResult
	}
	return 0
}

func (x *Feedback) GetCompleteTime() int64 {
	if x != nil {
		return x.CompleteTime
	}
	return 0
}

func (x *Feedback) GetCompleteRemark() string {
	if x != nil {
		return x.CompleteRemark
	}
	return ""
}

func (x *Feedback) GetCompleteAccount() string {
	if x != nil {
		return x.CompleteAccount
	}
	return ""
}

func (x *Feedback) GetCompleteNickName() string {
	if x != nil {
		return x.CompleteNickName
	}
	return ""
}

func (x *Feedback) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *Feedback) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Feedback) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_feedback_proto protoreflect.FileDescriptor

const file_pbentity_feedback_proto_rawDesc = "" +
	"\n" +
	"\x17pbentity/feedback.proto\x12\bpbentity\"\x9a\x04\n" +
	"\bFeedback\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x05R\x02Id\x12\"\n" +
	"\fFeedbackType\x18\x02 \x01(\x05R\fFeedbackType\x12\x16\n" +
	"\x06UserId\x18\x03 \x01(\rR\x06UserId\x12 \n" +
	"\vUserAccount\x18\x04 \x01(\tR\vUserAccount\x12\x16\n" +
	"\x06Images\x18\x05 \x01(\tR\x06Images\x12\x12\n" +
	"\x04Desc\x18\x06 \x01(\tR\x04Desc\x12\"\n" +
	"\fFeedbackTime\x18\a \x01(\x03R\fFeedbackTime\x12&\n" +
	"\x0eFeedbackStatus\x18\b \x01(\x05R\x0eFeedbackStatus\x12&\n" +
	"\x0eFeedbackResult\x18\t \x01(\x05R\x0eFeedbackResult\x12\"\n" +
	"\fCompleteTime\x18\n" +
	" \x01(\x03R\fCompleteTime\x12&\n" +
	"\x0eCompleteRemark\x18\v \x01(\tR\x0eCompleteRemark\x12(\n" +
	"\x0fCompleteAccount\x18\f \x01(\tR\x0fCompleteAccount\x12*\n" +
	"\x10CompleteNickName\x18\r \x01(\tR\x10CompleteNickName\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x0e \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x0f \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\x10 \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_feedback_proto_rawDescOnce sync.Once
	file_pbentity_feedback_proto_rawDescData []byte
)

func file_pbentity_feedback_proto_rawDescGZIP() []byte {
	file_pbentity_feedback_proto_rawDescOnce.Do(func() {
		file_pbentity_feedback_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_feedback_proto_rawDesc), len(file_pbentity_feedback_proto_rawDesc)))
	})
	return file_pbentity_feedback_proto_rawDescData
}

var file_pbentity_feedback_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_feedback_proto_goTypes = []any{
	(*Feedback)(nil), // 0: pbentity.Feedback
}
var file_pbentity_feedback_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_feedback_proto_init() }
func file_pbentity_feedback_proto_init() {
	if File_pbentity_feedback_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_feedback_proto_rawDesc), len(file_pbentity_feedback_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_feedback_proto_goTypes,
		DependencyIndexes: file_pbentity_feedback_proto_depIdxs,
		MessageInfos:      file_pbentity_feedback_proto_msgTypes,
	}.Build()
	File_pbentity_feedback_proto = out.File
	file_pbentity_feedback_proto_goTypes = nil
	file_pbentity_feedback_proto_depIdxs = nil
}
