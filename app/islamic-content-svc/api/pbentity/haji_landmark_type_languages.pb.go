// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/haji_landmark_type_languages.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiLandmarkTypeLanguages struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                   // 主键ID
	TypeId        uint64                 `protobuf:"varint,2,opt,name=TypeId,proto3" json:"TypeId,omitempty" dc:"地标类型ID，关联haji_landmark_type.id"` // 地标类型ID，关联haji_landmark_type.id
	LanguageId    uint32                 `protobuf:"varint,3,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID：0-中文，1-英文，2-印尼语"`   // 语言ID：0-中文，1-英文，2-印尼语
	TypeName      string                 `protobuf:"bytes,4,opt,name=TypeName,proto3" json:"TypeName,omitempty" dc:"类型名称"`                        // 类型名称
	CreateTime    uint64                 `protobuf:"varint,5,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`            // 创建时间（毫秒时间戳）
	UpdateTime    uint64                 `protobuf:"varint,6,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`            // 更新时间（毫秒时间戳）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HajiLandmarkTypeLanguages) Reset() {
	*x = HajiLandmarkTypeLanguages{}
	mi := &file_pbentity_haji_landmark_type_languages_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiLandmarkTypeLanguages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiLandmarkTypeLanguages) ProtoMessage() {}

func (x *HajiLandmarkTypeLanguages) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_landmark_type_languages_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiLandmarkTypeLanguages.ProtoReflect.Descriptor instead.
func (*HajiLandmarkTypeLanguages) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_landmark_type_languages_proto_rawDescGZIP(), []int{0}
}

func (x *HajiLandmarkTypeLanguages) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiLandmarkTypeLanguages) GetTypeId() uint64 {
	if x != nil {
		return x.TypeId
	}
	return 0
}

func (x *HajiLandmarkTypeLanguages) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *HajiLandmarkTypeLanguages) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *HajiLandmarkTypeLanguages) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiLandmarkTypeLanguages) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_landmark_type_languages_proto protoreflect.FileDescriptor

const file_pbentity_haji_landmark_type_languages_proto_rawDesc = "" +
	"\n" +
	"+pbentity/haji_landmark_type_languages.proto\x12\bpbentity\"\xbf\x01\n" +
	"\x19HajiLandmarkTypeLanguages\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x16\n" +
	"\x06TypeId\x18\x02 \x01(\x04R\x06TypeId\x12\x1e\n" +
	"\n" +
	"LanguageId\x18\x03 \x01(\rR\n" +
	"LanguageId\x12\x1a\n" +
	"\bTypeName\x18\x04 \x01(\tR\bTypeName\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x05 \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x06 \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_haji_landmark_type_languages_proto_rawDescOnce sync.Once
	file_pbentity_haji_landmark_type_languages_proto_rawDescData []byte
)

func file_pbentity_haji_landmark_type_languages_proto_rawDescGZIP() []byte {
	file_pbentity_haji_landmark_type_languages_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_landmark_type_languages_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_haji_landmark_type_languages_proto_rawDesc), len(file_pbentity_haji_landmark_type_languages_proto_rawDesc)))
	})
	return file_pbentity_haji_landmark_type_languages_proto_rawDescData
}

var file_pbentity_haji_landmark_type_languages_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_landmark_type_languages_proto_goTypes = []any{
	(*HajiLandmarkTypeLanguages)(nil), // 0: pbentity.HajiLandmarkTypeLanguages
}
var file_pbentity_haji_landmark_type_languages_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_landmark_type_languages_proto_init() }
func file_pbentity_haji_landmark_type_languages_proto_init() {
	if File_pbentity_haji_landmark_type_languages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_haji_landmark_type_languages_proto_rawDesc), len(file_pbentity_haji_landmark_type_languages_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_landmark_type_languages_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_landmark_type_languages_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_landmark_type_languages_proto_msgTypes,
	}.Build()
	File_pbentity_haji_landmark_type_languages_proto = out.File
	file_pbentity_haji_landmark_type_languages_proto_goTypes = nil
	file_pbentity_haji_landmark_type_languages_proto_depIdxs = nil
}
