// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/haji_doa_ringkas_content.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HajiDoaRingkasContent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                   // 主键ID
	DoaId          uint64                 `protobuf:"varint,2,opt,name=DoaId,proto3" json:"DoaId,omitempty" dc:"祈祷文ID"`                            // 祈祷文ID
	ContentOrder   int32                  `protobuf:"varint,3,opt,name=ContentOrder,proto3" json:"ContentOrder,omitempty" dc:"内容排序"`               // 内容排序
	Title          string                 `protobuf:"bytes,4,opt,name=Title,proto3" json:"Title,omitempty" dc:"内容标题（可为空）"`                         // 内容标题（可为空）
	MuqattaAt      string                 `protobuf:"bytes,5,opt,name=MuqattaAt,proto3" json:"MuqattaAt,omitempty" dc:"Muqattaʿāt断章字母（有则展示，无不展示）"` // Muqattaʿāt断章字母（有则展示，无不展示）
	ArabicText     string                 `protobuf:"bytes,6,opt,name=ArabicText,proto3" json:"ArabicText,omitempty" dc:"阿拉伯文原文"`                  // 阿拉伯文原文
	IndonesianText string                 `protobuf:"bytes,7,opt,name=IndonesianText,proto3" json:"IndonesianText,omitempty" dc:"印尼语翻译"`           // 印尼语翻译
	LatinText      string                 `protobuf:"bytes,8,opt,name=LatinText,proto3" json:"LatinText,omitempty" dc:"拉丁音译文本"`                    // 拉丁音译文本
	CreateTime     uint64                 `protobuf:"varint,9,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（毫秒时间戳）"`            // 创建时间（毫秒时间戳）
	UpdateTime     uint64                 `protobuf:"varint,10,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间（毫秒时间戳）"`           // 更新时间（毫秒时间戳）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *HajiDoaRingkasContent) Reset() {
	*x = HajiDoaRingkasContent{}
	mi := &file_pbentity_haji_doa_ringkas_content_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HajiDoaRingkasContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HajiDoaRingkasContent) ProtoMessage() {}

func (x *HajiDoaRingkasContent) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_haji_doa_ringkas_content_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HajiDoaRingkasContent.ProtoReflect.Descriptor instead.
func (*HajiDoaRingkasContent) Descriptor() ([]byte, []int) {
	return file_pbentity_haji_doa_ringkas_content_proto_rawDescGZIP(), []int{0}
}

func (x *HajiDoaRingkasContent) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HajiDoaRingkasContent) GetDoaId() uint64 {
	if x != nil {
		return x.DoaId
	}
	return 0
}

func (x *HajiDoaRingkasContent) GetContentOrder() int32 {
	if x != nil {
		return x.ContentOrder
	}
	return 0
}

func (x *HajiDoaRingkasContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *HajiDoaRingkasContent) GetMuqattaAt() string {
	if x != nil {
		return x.MuqattaAt
	}
	return ""
}

func (x *HajiDoaRingkasContent) GetArabicText() string {
	if x != nil {
		return x.ArabicText
	}
	return ""
}

func (x *HajiDoaRingkasContent) GetIndonesianText() string {
	if x != nil {
		return x.IndonesianText
	}
	return ""
}

func (x *HajiDoaRingkasContent) GetLatinText() string {
	if x != nil {
		return x.LatinText
	}
	return ""
}

func (x *HajiDoaRingkasContent) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *HajiDoaRingkasContent) GetUpdateTime() uint64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_haji_doa_ringkas_content_proto protoreflect.FileDescriptor

const file_pbentity_haji_doa_ringkas_content_proto_rawDesc = "" +
	"\n" +
	"'pbentity/haji_doa_ringkas_content.proto\x12\bpbentity\"\xbb\x02\n" +
	"\x15HajiDoaRingkasContent\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x14\n" +
	"\x05DoaId\x18\x02 \x01(\x04R\x05DoaId\x12\"\n" +
	"\fContentOrder\x18\x03 \x01(\x05R\fContentOrder\x12\x14\n" +
	"\x05Title\x18\x04 \x01(\tR\x05Title\x12\x1c\n" +
	"\tMuqattaAt\x18\x05 \x01(\tR\tMuqattaAt\x12\x1e\n" +
	"\n" +
	"ArabicText\x18\x06 \x01(\tR\n" +
	"ArabicText\x12&\n" +
	"\x0eIndonesianText\x18\a \x01(\tR\x0eIndonesianText\x12\x1c\n" +
	"\tLatinText\x18\b \x01(\tR\tLatinText\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\t \x01(\x04R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\n" +
	" \x01(\x04R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_haji_doa_ringkas_content_proto_rawDescOnce sync.Once
	file_pbentity_haji_doa_ringkas_content_proto_rawDescData []byte
)

func file_pbentity_haji_doa_ringkas_content_proto_rawDescGZIP() []byte {
	file_pbentity_haji_doa_ringkas_content_proto_rawDescOnce.Do(func() {
		file_pbentity_haji_doa_ringkas_content_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_haji_doa_ringkas_content_proto_rawDesc), len(file_pbentity_haji_doa_ringkas_content_proto_rawDesc)))
	})
	return file_pbentity_haji_doa_ringkas_content_proto_rawDescData
}

var file_pbentity_haji_doa_ringkas_content_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_haji_doa_ringkas_content_proto_goTypes = []any{
	(*HajiDoaRingkasContent)(nil), // 0: pbentity.HajiDoaRingkasContent
}
var file_pbentity_haji_doa_ringkas_content_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_haji_doa_ringkas_content_proto_init() }
func file_pbentity_haji_doa_ringkas_content_proto_init() {
	if File_pbentity_haji_doa_ringkas_content_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_haji_doa_ringkas_content_proto_rawDesc), len(file_pbentity_haji_doa_ringkas_content_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_haji_doa_ringkas_content_proto_goTypes,
		DependencyIndexes: file_pbentity_haji_doa_ringkas_content_proto_depIdxs,
		MessageInfos:      file_pbentity_haji_doa_ringkas_content_proto_msgTypes,
	}.Build()
	File_pbentity_haji_doa_ringkas_content_proto = out.File
	file_pbentity_haji_doa_ringkas_content_proto_goTypes = nil
	file_pbentity_haji_doa_ringkas_content_proto_depIdxs = nil
}
