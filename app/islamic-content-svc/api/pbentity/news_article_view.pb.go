// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/news_article_view.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsArticleView struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                     //
	UserId        uint32                 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户id"`                   // 用户id
	ArticleId     uint32                 `protobuf:"varint,3,opt,name=ArticleId,proto3" json:"ArticleId,omitempty" dc:"article_id"`       // article_id
	ArticleName   string                 `protobuf:"bytes,4,opt,name=ArticleName,proto3" json:"ArticleName,omitempty" dc:"名称"`            // 名称
	CreateTime    int64                  `protobuf:"varint,5,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间（注册时间）"`     // 创建时间（注册时间）
	UpdateTime    int64                  `protobuf:"varint,6,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间，0代表创建后未更新"` // 更新时间，0代表创建后未更新
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsArticleView) Reset() {
	*x = NewsArticleView{}
	mi := &file_pbentity_news_article_view_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsArticleView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsArticleView) ProtoMessage() {}

func (x *NewsArticleView) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_article_view_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsArticleView.ProtoReflect.Descriptor instead.
func (*NewsArticleView) Descriptor() ([]byte, []int) {
	return file_pbentity_news_article_view_proto_rawDescGZIP(), []int{0}
}

func (x *NewsArticleView) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsArticleView) GetUserId() uint32 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *NewsArticleView) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *NewsArticleView) GetArticleName() string {
	if x != nil {
		return x.ArticleName
	}
	return ""
}

func (x *NewsArticleView) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsArticleView) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

var File_pbentity_news_article_view_proto protoreflect.FileDescriptor

const file_pbentity_news_article_view_proto_rawDesc = "" +
	"\n" +
	" pbentity/news_article_view.proto\x12\bpbentity\"\xb9\x01\n" +
	"\x0fNewsArticleView\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x16\n" +
	"\x06UserId\x18\x02 \x01(\rR\x06UserId\x12\x1c\n" +
	"\tArticleId\x18\x03 \x01(\rR\tArticleId\x12 \n" +
	"\vArticleName\x18\x04 \x01(\tR\vArticleName\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\x05 \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x06 \x01(\x03R\n" +
	"UpdateTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_article_view_proto_rawDescOnce sync.Once
	file_pbentity_news_article_view_proto_rawDescData []byte
)

func file_pbentity_news_article_view_proto_rawDescGZIP() []byte {
	file_pbentity_news_article_view_proto_rawDescOnce.Do(func() {
		file_pbentity_news_article_view_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_article_view_proto_rawDesc), len(file_pbentity_news_article_view_proto_rawDesc)))
	})
	return file_pbentity_news_article_view_proto_rawDescData
}

var file_pbentity_news_article_view_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_article_view_proto_goTypes = []any{
	(*NewsArticleView)(nil), // 0: pbentity.NewsArticleView
}
var file_pbentity_news_article_view_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_article_view_proto_init() }
func file_pbentity_news_article_view_proto_init() {
	if File_pbentity_news_article_view_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_article_view_proto_rawDesc), len(file_pbentity_news_article_view_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_article_view_proto_goTypes,
		DependencyIndexes: file_pbentity_news_article_view_proto_depIdxs,
		MessageInfos:      file_pbentity_news_article_view_proto_msgTypes,
	}.Build()
	File_pbentity_news_article_view_proto = out.File
	file_pbentity_news_article_view_proto_goTypes = nil
	file_pbentity_news_article_view_proto_depIdxs = nil
}
