// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message Feedback {
    int32  Id               = 1;  //
    int32  FeedbackType     = 2;  // 反馈类型 1 系统故障 2 功能建议
    uint32 UserId           = 3;  // 用户id
    string UserAccount      = 4;  // 用户账号
    string Images           = 5;  // 反馈图片 json字符串
    string Desc             = 6;  // 反馈内容
    int64  FeedbackTime     = 7;  // 反馈时间ms
    int32  FeedbackStatus   = 8;  // 反馈状态 1 未处理 2已处理
    int32  FeedbackResult   = 9;  // 反馈结果 1 无效反馈 2有效反馈
    int64  CompleteTime     = 10; // 处理时间ms
    string CompleteRemark   = 11; // 处理备注
    string CompleteAccount  = 12; // 处理人
    string CompleteNickName = 13; // 处理人昵称
    int64  CreateTime       = 14; // 创建时间
    int64  UpdateTime       = 15; // 更新时间
    int64  DeleteTime       = 16; // 删除时间
}