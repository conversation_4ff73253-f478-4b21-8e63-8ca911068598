// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message UmrahLandmarkType {
    uint64 Id         = 1; // 主键ID
    string IconUrl    = 2; // 图标路径
    uint64 CreateTime = 3; // 创建时间（毫秒时间戳）
    uint64 UpdateTime = 4; // 更新时间（毫秒时间戳）
}