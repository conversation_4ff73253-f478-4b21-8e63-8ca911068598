// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message WisdomCate {
    uint32 Id            = 1;  //
    uint32 IsZh          = 2;  // 是否中文，0-否，1-是
    uint32 IsEn          = 3;  // 是否英文，0-否，1-是
    uint32 IsId          = 4;  // 是否印尼文，0-否，1-是
    int32  IsOpen        = 5;  // 状态 [ 1 启用 2 禁用]
    int32  Sort          = 6;  // 排序
    int32  CateCount     = 7;  // 分类下的文章总数
    string Remark        = 8;  // 备注
    string CreateAccount = 9;  // 创建者
    string UpdateAccount = 10; // 更新者
    int64  CreateTime    = 11; //
    int64  UpdateTime    = 12; //
    int64  DeleteTime    = 13; //
}