// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message FaqQuestion {
    uint32 Id            = 1;  //
    uint32 IsZh          = 2;  // 是否中文，0-否，1-是
    uint32 IsEn          = 3;  // 是否英文，0-否，1-是
    uint32 IsId          = 4;  // 是否印尼文，0-否，1-是
    uint32 FaqCateId     = 5;  //
    int32  IsPublish     = 6;  // 发布状态 [ 0未发布 1已发布 2 已下线]
    int32  Sort          = 7;  // 排序
    uint64 PublishTime   = 8;  // 发布时间
    int32  Views         = 9;  // 浏览量
    string CreateAccount = 10; // 创建者
    string UpdateAccount = 11; // 更新者
    int64  CreateTime    = 12; //
    int64  UpdateTime    = 13; //
    int64  DeleteTime    = 14; //
}