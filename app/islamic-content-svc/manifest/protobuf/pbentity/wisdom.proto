// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";



message Wisdom {
    uint32 Id            = 1;  //
    uint32 IsZh          = 2;  // 是否中文，0-否，1-是
    uint32 IsEn          = 3;  // 是否英文，0-否，1-是
    uint32 IsId          = 4;  // 是否印尼文，0-否，1-是
    uint32 WisdomCateId  = 5;  //
    int32  IsOpen        = 6;  // 状态 [ 1 启用 2 禁用]
    int32  Sort          = 7;  // 排序
    int32  Views         = 8;  // 浏览量
    uint32 ArticleId     = 9;  // 文章id
    string CreateAccount = 10; // 创建者
    string UpdateAccount = 11; // 更新者
    int64  CreateTime    = 12; //
    int64  UpdateTime    = 13; //
    int64  DeleteTime    = 14; //
}