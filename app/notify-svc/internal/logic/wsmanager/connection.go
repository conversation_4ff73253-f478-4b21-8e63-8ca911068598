package wsmanager

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gorilla/websocket"
	"google.golang.org/protobuf/proto"
	"halalplus/app/notify-svc/internal/service"
	"sync"
)

type Connection struct {
	ctx      context.Context
	wsConn   *websocket.Conn
	userId   uint64
	deviceId string
	mu       sync.Mutex
	once     sync.Once
	isClosed bool
	isAuth   bool
}

func (c *Connection) SetAuth(userId uint64, deviceId string) {
	c.once.Do(func() {
		c.userId = userId
		c.deviceId = deviceId
		c.isAuth = true
	})
}

func (c *Connection) IsAuth() bool {
	return c.isAuth
}

func (c *Connection) Ping() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.wsConn.WriteMessage(websocket.PingMessage, nil)
}

func (c *Connection) Pong() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.wsConn.WriteMessage(websocket.PongMessage, nil)
}

var _ service.IWsConnection = &Connection{}

func (c *Connection) SendMessage(message proto.Message) error {
	b, err := proto.Marshal(message)
	if err != nil {
		return err
	}
	c.mu.Lock()
	defer c.mu.Unlock()

	return c.wsConn.WriteMessage(websocket.BinaryMessage, b)
}

func (c *Connection) SendBinaryMessage(b []byte) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.wsConn.WriteMessage(websocket.BinaryMessage, b)
}

func (c *Connection) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.isClosed = true
	g.Log().Debug(nil, "close connection")
	if c.wsConn != nil {
		return c.wsConn.Close()
	}
	return nil
}

func (c *Connection) IsClosed() bool {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.isAuth = false
	return c.isClosed
}
