// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"halalplus/app/user-account-svc/internal/model"
)

type (
	IVerify interface {
		// 登录验证码
		SendLoginCode(ctx context.Context, mp *model.PhoneInfo) error
		// 校验登录验证码
		VerifyLoginCode(ctx context.Context, mp *model.PhoneInfo, code string) (bool, error)
		// 绑定手机号码验证码
		SendBindPhoneCode(ctx context.Context, mp *model.PhoneInfo) error
		// 校验绑定手机号码验证码
		VerifyBindPhoneCode(ctx context.Context, mp *model.PhoneInfo, code string) (bool, error)
		SendMyPhoneCode(ctx context.Context, uid uint64) error
		// 验证登录手机号
		VerifyMyPhoneCode(ctx context.Context, uid uint64, code string) (bool, error)
	}
)

var (
	localVerify IVerify
)

func Verify() IVerify {
	if localVerify == nil {
		panic("implement not found for interface IVerify, forgot register?")
	}
	return localVerify
}

func RegisterVerify(i IVerify) {
	localVerify = i
}
