package verify

import (
	"context"
	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"halalplus/app/user-account-svc/internal/consts"
	"halalplus/app/user-account-svc/internal/dao"
	"halalplus/app/user-account-svc/internal/model"
	"halalplus/app/user-account-svc/internal/service"
	"halalplus/utility"
)

type (
	sVerify struct {
	}
)

func init() {
	service.RegisterVerify(New())
}

func New() service.IVerify {
	u := &sVerify{}
	return u
}

const (
	CodeTtl  = int64(60 * 5) // 相同验证码的时间，防止重复发送没法验证的问题
	ValidTtl = int64(60 * 1) // 验证码有效期
)

// 登录验证码
func (s *sVerify) SendLoginCode(ctx context.Context, mp *model.PhoneInfo) error {
	code, err := s.genPhoneCode(ctx, mp)
	if err != nil {
		return err
	}
	return s.doSendCode(ctx, mp, code)
}

// 校验登录验证码
func (s *sVerify) VerifyLoginCode(ctx context.Context, mp *model.PhoneInfo, code string) (bool, error) {
	return s.checkPhoneCode(ctx, mp, code)
}

// 绑定手机号码验证码
func (s *sVerify) SendBindPhoneCode(ctx context.Context, mp *model.PhoneInfo) error {
	code, err := s.genPhoneCode(ctx, mp)
	if err != nil {
		return err
	}

	return s.doSendCode(ctx, mp, code)
}

// 校验绑定手机号码验证码
func (s *sVerify) VerifyBindPhoneCode(ctx context.Context, mp *model.PhoneInfo, code string) (bool, error) {
	return s.checkPhoneCode(ctx, mp, code)
	return true, nil
}

// 执行发送短信
func (s *sVerify) doSendCode(ctx context.Context, mp *model.PhoneInfo, code string) error {
	g.Log().Debug(ctx, "doSendCode", mp.Key(), utility.MaskString(code, 2, 1))
	// TODO: 发送短信验证码
	return nil
}

// 随机6位数字字符串,保存到redis
func (s *sVerify) genPhoneCode(ctx context.Context, mp *model.PhoneInfo) (code string, err error) {
	code = utility.RandCode(0, 6)
	redisKey := consts.KeyPhoneOpt(mp.GetAreaCode(), mp.GetPhoneNum())

	// 重复发送，用相同的验证码
	ttl := CodeTtl
	val, err := g.Redis().GetEX(ctx, redisKey, gredis.GetEXOption{
		// 更新有效期
		TTLOption: gredis.TTLOption{
			EX: &ttl,
		},
	})
	if err == nil && !g.IsEmpty(val.String()) {
		return val.String(), nil
	}

	// 保存到redis
	err = g.Redis().SetEX(ctx, redisKey, code, CodeTtl)
	if err != nil {
		g.Log().Error(ctx, err)
		return "", err
	}

	return code, nil
}

// 检查redis记录的验证码
func (s *sVerify) checkPhoneCode(ctx context.Context, mp *model.PhoneInfo, code string) (ok bool, err error) {
	redisKey := consts.KeyPhoneOpt(mp.GetAreaCode(), mp.GetPhoneNum())
	redis := g.Redis()
	ttlVal, err := redis.TTL(ctx, redisKey)
	if err != nil || ttlVal < CodeTtl-ValidTtl {
		// key不存在或者过了有效期
		return false, err
	}

	val, err := redis.Get(ctx, redisKey)
	if err != nil {
		g.Log().Error(ctx, err)
		return false, err
	}
	// FIXME: 万能验证码123456
	if len(code) > 3 && (val.String() == code || "123456" == code) {
		// 验证通过，清理
		redis.Del(ctx, redisKey)
		return true, nil
	}

	return false, nil
}

func (s *sVerify) SendMyPhoneCode(ctx context.Context, uid uint64) error {
	mp, err := dao.User.GetMyPHone(ctx, uid)
	if err != nil {
		g.Log().Error(ctx, err)
		return err
	}
	code, err := s.genPhoneCode(ctx, mp)
	if err != nil {
		return err
	}
	return s.doSendCode(ctx, mp, code)
}

// 验证登录手机号
func (s *sVerify) VerifyMyPhoneCode(ctx context.Context, uid uint64, code string) (bool, error) {
	mp, err := dao.User.GetMyPHone(ctx, uid)
	if err != nil {
		g.Log().Error(ctx, err)
		return false, err
	}
	return s.checkPhoneCode(ctx, mp, code)
}
